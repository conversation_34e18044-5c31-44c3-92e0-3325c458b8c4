<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">

    <title><PERSON><PERSON></title>

    <link rel="stylesheet" href="../../../vendor/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="../css/app.css">
    <link rel="stylesheet" href="../css/sayonika-store.css">
</head>
<body>
<div id="app" :class="['os-'+system_platform, appx ? 'is-appx': 'is-standard']"
     :style="{'background-image': backgroundImageStyle}">
    <ddmm-onboarding v-if="onboarding" :style="{'background-image': backgroundImageStyle}"
                     @close="onboarding = false"></ddmm-onboarding>
    <ddmm-drop-overlay v-if="dropping_mod" @end="dropping_mod = false" @file="showInstallMod"></ddmm-drop-overlay>
    <div class="cover" v-if="running_cover.display" :style="{'background-image': backgroundImageStyle}">
        <h1>{{running_cover.title}}</h1>
        <p>{{running_cover.description}}</p>
        <template v-if="!running_cover.dismissable">
            <br>
            <p>
                <button class="primary" @click="showFile(running_cover.folder_path)"><i class="fas fa-folder fa-fw"></i>
                    {{_("main.running_cover.button_browse")}}
                </button>
            </p>
        </template>
        <br>
        <p>
            <button v-if="running_cover.dismissable" @click="running_cover.display = false" class="primary"><i
                    class="fas fa-times fa-fw"></i> {{_("main.running_cover.button_close")}}
            </button>
        </p>
    </div>

    <div class="cover crash" v-if="crash_cover.display" :style="{'background-image': backgroundImageCrashStyle}">
        <h1>{{crash_cover.title}}</h1>
        <p>{{crash_cover.description}}</p>
        <br>
        <p v-if="crash_cover.fatal">
            <button class="danger" @click="windowClose"><i class="fas fa-times fa-fw"></i>
                {{_("renderer.crash_cover.button_close")}}
            </button>
        </p>
        <p v-else>
            <button class="danger" @click="crash_cover.display = false"><i class="fas fa-arrow-right fa-fw"></i>
                {{_("renderer.crash_cover.button_continue")}}
            </button>
        </p>
        <br>
        <span>{{crash_cover.stacktrace}}</span>
    </div>

    <div class="cover" v-if="prompt_cover.display" :style="{'background-image': backgroundImageStyle}">
        <h1>{{prompt_cover.title}}</h1>
        <p>{{prompt_cover.description}}</p>
        <br>
        <p>
            <button :class="[prompt_cover.affirmative_style]" @click="closePrompt(true)"><i
                    class="fas fa-check fa-fw"></i> {{prompt_cover.button_affirmative}}
            </button>
            <button class="secondary" @click="closePrompt(false)"><i class="fas fa-times fa-fw"></i>
                {{prompt_cover.button_negative}}
            </button>
        </p>
    </div>

    <div class="cover" v-if="input_cover.display" :style="{'background-image': backgroundImageStyle}">
        <h1>{{input_cover.title}}</h1>
        <p>{{input_cover.description}}</p>
        <br>
        <div><input type="text" v-model="input_cover.input" autofocus ref="input_cover_field"></div>
        <br>
        <p>
            <button class="primary" @click="closeInput(input_cover.input)"><i class="fas fa-check fa-fw"></i>
                {{input_cover.button_affirmative}}
            </button>
            <button class="secondary" @click="closeInput(null)"><i class="fas fa-times fa-fw"></i>
                {{input_cover.button_negative}}
            </button>
        </p>
    </div>

    <!-- DDLC Crash Dialog -->
    <div class="cover ddlc-crash" v-if="ddlc_crash_dialog.display" :style="{'background-image': backgroundImageCrashStyle}">
        <div class="crash-dialog-content">
            <h1><i class="fas fa-exclamation-triangle"></i> DDLC Crashed</h1>
            <p>Doki Doki Literature Club has unexpectedly crashed.</p>

            <div class="crash-details">
                <p><strong>Error Details:</strong> {{getCrashTypeDescription()}}</p>
                <p><strong>Install:</strong> {{ddlc_crash_dialog.folderName}}</p>
                <p><strong>Time:</strong> {{ddlc_crash_dialog.crashInfo ? new Date(ddlc_crash_dialog.crashInfo.timestamp).toLocaleString() : 'Unknown'}}</p>
            </div>

            <div class="crash-actions">
                <button class="primary" @click="relaunchDDLC">
                    <i class="fas fa-redo fa-fw"></i> Relaunch Game
                </button>
                <button class="secondary" @click="backToMenu">
                    <i class="fas fa-arrow-left fa-fw"></i> Back to Menu
                </button>
                <button class="tertiary" @click="showFile(ddlc_crash_dialog.installPath)">
                    <i class="fas fa-folder fa-fw"></i> Open Install Folder
                </button>
            </div>

            <div class="crash-help">
                <p><small>If this keeps happening, try reinstalling the mod or checking for updates.</small></p>
            </div>
        </div>
    </div>

    <div class="titlebar">
        <div class="app-title">
            <span>{{app_name}}</span>
            <small>v{{app_version}}</small>
        </div>
        <div class="window-buttons">
            <div v-if="app_updating === 'checking'" :title="_('renderer.window_controls.update.checking')">
                <i class="fas fa-sync-alt fa-spin"></i>
            </div>
            <div v-else-if="app_updating === 'available'" :title="_('renderer.window_controls.update.available')"
                 @click="downloadUpdate">
                <i class="fas fa-download window-button-draw-attention"></i>
            </div>
            <div v-else-if="app_updating === 'downloading'" :title="_('renderer.window_controls.update.downloading')">
                <i class="fas fa-sync-alt fa-spin"></i>
            </div>
            <div v-else-if="app_updating === 'downloaded'" :title="_('renderer.window_controls.update.downloaded')"
                 @click="restart">
                <i class="fas fa-download"></i>
            </div>

            <div @click="viewAnnouncement" :title="_('renderer.window_controls.announcement')"
                 v-if="announcement.active"><i
                    :class="{'fas': true, 'fa-bell': true, 'window-button-draw-attention': flashAnnouncement}"></i>
            </div>



            <div @click="showSayonikaLogin" v-if="!getSayonikaUser()">{{_('renderer.window_controls.sayonika_login')}}</div>
            <div @click="showSayonikaUserMenu" v-else style="max-width: 200px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">{{getSayonikaUser().display_name || getSayonikaUser().username}}</div>

            <div @click="toggleDevTools" :title="_('renderer.window_controls.devtools')"><i
                    class="fas fa-code fa-fw"></i></div>

            <template v-if="!system_borders">
                <div @click="windowMinimise" :title="_('renderer.window_controls.minimise')"><i
                        class="far fa-window-minimize fa-fw"></i></div>
                <div @click="windowMaximise" :title="_('renderer.window_controls.maximise')"><i
                        class="far fa-window-maximize fa-fw"></i></div>
                <div @click="windowClose" :title="_('renderer.window_controls.close')"><i
                        class="fas fa-times fa-fw"></i></div>
            </template>
        </div>
    </div>
    <transition :name="pageTransition" mode="out-in">
        <component
                :is="currentTabComponent"
                :key="tab"
                @set_background="setBackground"
        ></component>
    </transition>
    <div class="navbar">
        <div class="nav-links">
            <div v-for="t in tabs" v-if="!t.hidden" :class="{'active': t.id === tab}" @click="navigateToTab(t.id, 'fade')">{{t.name}}</div>
        </div>
    </div>
</div>


<!-- Firebase app script removed - cloud saves disabled -->

<!-- Firebase scripts removed - cloud saves disabled -->
<script src="../../../vendor/fuse.js"></script>
<script src="../../../vendor/vue.js"></script>
<script src="../js/sayonika-config.js"></script>
<script src="../js/sayonika-auth.js"></script>
<script src="../js/components/OnboardingOverlay.js"></script>
<script src="../js/components/DropOverlay.js"></script>
<script src="../js/components/tabs/ModsTab.js"></script>
<script src="../js/components/tabs/StorePlaceholderTab.js"></script>
<script src="../js/components/tabs/OptionsTab.js"></script>
<script src="../js/components/tabs/AboutTab.js"></script>
<script src="../js/components/tabs/EditInstanceTab.js"></script>
<script src="../js/app.js"></script>
</body>
</html>