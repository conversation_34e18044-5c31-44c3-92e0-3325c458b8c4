<%- include('../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Hero Section (only on main docs page) -->
    <% if (currentPath === '/docs' || currentPath === '/docs/') { %>
    <section class="docs-hero">
        <div class="container">
            <div class="docs-hero-content">
                <div class="hero-badge">
                    <i class="fas fa-book"></i>
                    <span>Documentation</span>
                </div>
                <h1 class="hero-title">Sayonika Documentation</h1>
                <p class="hero-subtitle">
                    Complete guide to setting up, using, and developing with Sayonika - the ultimate DDLC mod platform
                </p>
                <div class="hero-meta">
                    <div class="version-info">
                        <span class="version-badge">v2.1</span>
                        <span class="last-updated">Last updated: January 2025</span>
                    </div>
                    <div class="hero-actions">
                        <a href="/docs/installation" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            Quick Start
                        </a>
                        <a href="/docs/api" class="btn btn-outline">
                            <i class="fas fa-code"></i>
                            API Reference
                        </a>
                    </div>
                </div>
            </div>

            <!-- Feature Cards -->
            <div class="hero-features">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>Quick Setup</h3>
                    <p>Get Sayonika running in minutes with our step-by-step guide</p>
                </div>
                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>REST API</h3>
                    <p>Powerful API with comprehensive endpoints and examples</p>
                </div>
                <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Secure</h3>
                    <p>Built-in authentication, rate limiting, and security features</p>
                </div>
                <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <h3>Extensible</h3>
                    <p>Modular design for easy customization and integration</p>
                </div>
            </div>
        </div>
    </section>
    <% } %>

    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Overview Section -->
                    <section class="docs-section" id="overview">
                        <h2>Overview</h2>
                        <div class="section-content">
                            <p class="lead">
                                Sayonika is a modern, feature-rich platform for hosting and managing Doki Doki Literature Club mods.
                                Built with Node.js and designed for both developers and end-users, it provides a comprehensive
                                solution for mod distribution, user management, and community engagement.
                            </p>

                            <div class="feature-grid">
                                <div class="feature-item">
                                    <h4><i class="fas fa-users"></i> User Management</h4>
                                    <p>Complete user authentication system with OAuth support for GitHub and Discord, role-based permissions, profile management, and account linking.</p>
                                </div>

                                <div class="feature-item">
                                    <h4><i class="fas fa-puzzle-piece"></i> Mod Management</h4>
                                    <p>Upload, review, and distribute DDLC mods with automatic validation, thumbnail generation, version control, and comment system.</p>
                                </div>

                                <div class="feature-item">
                                    <h4><i class="fas fa-shield-alt"></i> Security</h4>
                                    <p>Built-in security features including rate limiting, CSRF protection, input validation, secure file handling, and maintenance mode.</p>
                                </div>

                                <div class="feature-item">
                                    <h4><i class="fas fa-code"></i> REST API</h4>
                                    <p>Comprehensive REST API with JWT authentication, detailed documentation, multi-language code examples, and admin endpoints.</p>
                                </div>

                                <div class="feature-item">
                                    <h4><i class="fas fa-trophy"></i> Gamification</h4>
                                    <p>Achievement system, user rankings, mod ratings, user levels, titles, and community features to encourage engagement.</p>
                                </div>

                                <div class="feature-item">
                                    <h4><i class="fas fa-mobile-alt"></i> Responsive Design</h4>
                                    <p>Modern, mobile-first design with dark/light theme support, accessibility features, and admin dashboard.</p>
                                </div>
                            </div>

                            <div class="quick-links">
                                <h3>Quick Links</h3>
                                <div class="link-grid">
                                    <a href="/docs/installation" class="quick-link">
                                        <i class="fas fa-download"></i>
                                        <div>
                                            <h4>Installation</h4>
                                            <p>Get Sayonika up and running</p>
                                        </div>
                                    </a>

                                    <a href="/docs/api" class="quick-link">
                                        <i class="fas fa-code"></i>
                                        <div>
                                            <h4>API Reference</h4>
                                            <p>Explore the REST API</p>
                                        </div>
                                    </a>



                                    <a href="/docs/customization" class="quick-link">
                                        <i class="fas fa-paint-brush"></i>
                                        <div>
                                            <h4>Customization</h4>
                                            <p>Customize your instance</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- System Requirements -->
                    <section class="docs-section" id="requirements">
                        <h2>System Requirements</h2>
                        <div class="section-content">
                            <div class="requirements-grid">
                                <div class="requirement-item">
                                    <h4><i class="fab fa-node-js"></i> Node.js</h4>
                                    <p>Version 16.0 or higher</p>
                                </div>

                                <div class="requirement-item">
                                    <h4><i class="fas fa-database"></i> Database</h4>
                                    <p>SQLite 3.0+ (included)</p>
                                </div>

                                <div class="requirement-item">
                                    <h4><i class="fas fa-memory"></i> Memory</h4>
                                    <p>Minimum 512MB RAM</p>
                                </div>

                                <div class="requirement-item">
                                    <h4><i class="fas fa-hdd"></i> Storage</h4>
                                    <p>1GB+ free space</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../partials/footer') %>
