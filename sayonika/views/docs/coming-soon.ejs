<%- include('../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Coming Soon Section -->
                    <section class="docs-section" id="coming-soon">
                        <h2><%= pageTitle || 'Documentation Page' %></h2>
                        <div class="section-content">
                            <div class="coming-soon-container">
                                <div class="coming-soon-icon">
                                    <i class="fas fa-hammer"></i>
                                </div>
                                <h3>Coming Soon</h3>
                                <p class="lead">
                                    This documentation page is currently under construction.
                                    We're working hard to bring you comprehensive documentation for this section.
                                </p>

                                <div class="alert info">
                                    <i class="fas fa-info-circle"></i>
                                    <div>
                                        <strong>In the meantime:</strong> Check out our
                                        <a href="/docs">overview</a>,
                                        <a href="/docs/installation">installation guide</a>, or
                                        <a href="/docs/api">API reference</a> for available documentation.
                                    </div>
                                </div>

                                <div class="quick-links">
                                    <h4>Available Documentation</h4>
                                    <div class="link-grid">
                                        <a href="/docs" class="quick-link">
                                            <i class="fas fa-home"></i>
                                            <div>
                                                <h5>Overview</h5>
                                                <p>Platform overview and features</p>
                                            </div>
                                        </a>

                                        <a href="/docs/installation" class="quick-link">
                                            <i class="fas fa-download"></i>
                                            <div>
                                                <h5>Installation</h5>
                                                <p>Get Sayonika up and running</p>
                                            </div>
                                        </a>

                                        <a href="/docs/api" class="quick-link">
                                            <i class="fas fa-code"></i>
                                            <div>
                                                <h5>API Reference</h5>
                                                <p>REST API documentation</p>
                                            </div>
                                        </a>
                                    </div>
                                </div>

                                <div class="help-section">
                                    <h4>Need Help?</h4>
                                    <p>
                                        If you have questions or need assistance, feel free to
                                        <a href="https://github.com/Dynamicaaa/Sayonika/issues/new" target="_blank">create an issue</a>
                                        on our GitHub repository.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../partials/footer') %>
