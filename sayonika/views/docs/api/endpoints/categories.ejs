<%- include('../../../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../../../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Category Endpoints -->
                    <section class="docs-section" id="category-endpoints">
                        <h2>Category & Tag Endpoints</h2>
                        <div class="section-content">
                            <p class="lead">
                                Category and tag endpoints allow you to browse mod categories, retrieve category information,
                                and explore available tags for filtering and organizing mods.
                            </p>

                            <!-- List Categories -->
                            <div class="endpoint">
                                <h3 id="list-categories">
                                    <span class="method get">GET</span>
                                    <code>/api/categories</code>
                                </h3>
                                <p>Get a list of all available mod categories.</p>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Full Mods",
      "slug": "full-mods",
      "description": "Complete story modifications that replace the original DDLC experience",
      "mod_count": 45,
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "Mini Mods",
      "slug": "mini-mods",
      "description": "Smaller modifications and additions to the base game",
      "mod_count": 23,
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": 3,
      "name": "Tools & Utilities",
      "slug": "tools-utilities",
      "description": "Development tools and utilities for DDLC modding",
      "mod_count": 12,
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": 4,
      "name": "Assets",
      "slug": "assets",
      "description": "Sprites, backgrounds, music, and other game assets",
      "mod_count": 18,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/categories"</code></pre>
                                </div>
                            </div>

                            <!-- Get Category -->
                            <div class="endpoint">
                                <h3 id="get-category">
                                    <span class="method get">GET</span>
                                    <code>/api/categories/{slug}</code>
                                </h3>
                                <p>Get detailed information about a specific category.</p>

                                <h4>Path Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>slug</code></td>
                                            <td>string</td>
                                            <td>Category slug identifier</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Full Mods",
    "slug": "full-mods",
    "description": "Complete story modifications that replace the original DDLC experience",
    "mod_count": 45,
    "created_at": "2024-01-01T00:00:00Z",
    "popular_tags": [
      "romance",
      "comedy",
      "drama",
      "original-characters",
      "alternate-ending"
    ],
    "recent_mods": [
      {
        "id": 123,
        "title": "Recent Full Mod",
        "slug": "recent-full-mod",
        "thumbnail_url": "https://example.com/thumbnails/mod-123.jpg",
        "downloads": 500,
        "rating": 4.5,
        "created_at": "2024-01-10T08:00:00Z"
      }
    ]
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/categories/full-mods"</code></pre>
                                </div>
                            </div>

                            <!-- Get Category Mods -->
                            <div class="endpoint">
                                <h3 id="get-category-mods">
                                    <span class="method get">GET</span>
                                    <code>/api/categories/{slug}/mods</code>
                                </h3>
                                <p>Get a paginated list of mods in a specific category.</p>

                                <h4>Path Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>slug</code></td>
                                            <td>string</td>
                                            <td>Category slug identifier</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Query Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Default</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>page</code></td>
                                            <td>integer</td>
                                            <td>Page number</td>
                                            <td>1</td>
                                        </tr>
                                        <tr>
                                            <td><code>per_page</code></td>
                                            <td>integer</td>
                                            <td>Items per page (max: 100)</td>
                                            <td>20</td>
                                        </tr>
                                        <tr>
                                            <td><code>sort</code></td>
                                            <td>string</td>
                                            <td>Sort field: created_at, updated_at, downloads, rating</td>
                                            <td>created_at</td>
                                        </tr>
                                        <tr>
                                            <td><code>order</code></td>
                                            <td>string</td>
                                            <td>Sort order: asc, desc</td>
                                            <td>desc</td>
                                        </tr>
                                        <tr>
                                            <td><code>tags</code></td>
                                            <td>string</td>
                                            <td>Comma-separated list of tags to filter by</td>
                                            <td>-</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "Category Mod Example",
      "slug": "category-mod-example",
      "description": "A mod in this category",
      "version": "1.0.0",
      "downloads": 500,
      "rating": 4.5,
      "status": "approved",
      "thumbnail_url": "https://example.com/thumbnails/mod-123.jpg",
      "created_at": "2024-01-10T08:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "author": {
        "id": 456,
        "username": "modder123",
        "display_name": "Awesome Modder"
      },
      "tags": ["romance", "comedy"]
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 45,
    "total_pages": 3,
    "has_next": true,
    "has_prev": false
  },
  "category": {
    "id": 1,
    "name": "Full Mods",
    "slug": "full-mods"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code># Get all mods in category
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/categories/full-mods/mods"

# Get romance mods in category, sorted by rating
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/categories/full-mods/mods?tags=romance&sort=rating&order=desc"</code></pre>
                                </div>
                            </div>

                            <!-- List Tags -->
                            <div class="endpoint">
                                <h3 id="list-tags">
                                    <span class="method get">GET</span>
                                    <code>/api/tags</code>
                                </h3>
                                <p>Get a list of all available tags with usage statistics.</p>

                                <h4>Query Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Default</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>popular</code></td>
                                            <td>boolean</td>
                                            <td>Only return popular tags (used by 3+ mods)</td>
                                            <td>false</td>
                                        </tr>
                                        <tr>
                                            <td><code>limit</code></td>
                                            <td>integer</td>
                                            <td>Maximum number of tags to return</td>
                                            <td>100</td>
                                        </tr>
                                        <tr>
                                            <td><code>sort</code></td>
                                            <td>string</td>
                                            <td>Sort by: name, usage_count</td>
                                            <td>usage_count</td>
                                        </tr>
                                        <tr>
                                            <td><code>order</code></td>
                                            <td>string</td>
                                            <td>Sort order: asc, desc</td>
                                            <td>desc</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": [
    {
      "name": "romance",
      "slug": "romance",
      "usage_count": 25,
      "description": "Romantic storylines and relationships"
    },
    {
      "name": "comedy",
      "slug": "comedy",
      "usage_count": 18,
      "description": "Humorous and lighthearted content"
    },
    {
      "name": "drama",
      "slug": "drama",
      "usage_count": 15,
      "description": "Dramatic and emotional storylines"
    },
    {
      "name": "original-characters",
      "slug": "original-characters",
      "usage_count": 12,
      "description": "Features new original characters"
    },
    {
      "name": "alternate-ending",
      "slug": "alternate-ending",
      "usage_count": 10,
      "description": "Alternative endings to the original story"
    }
  ]
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code># Get all tags
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/tags"

# Get popular tags only
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/tags?popular=true&limit=10"</code></pre>
                                </div>
                            </div>

                            <!-- Get Tag Mods -->
                            <div class="endpoint">
                                <h3 id="get-tag-mods">
                                    <span class="method get">GET</span>
                                    <code>/api/tags/{slug}/mods</code>
                                </h3>
                                <p>Get a paginated list of mods with a specific tag.</p>

                                <h4>Path Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>slug</code></td>
                                            <td>string</td>
                                            <td>Tag slug identifier</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Query Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Default</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>page</code></td>
                                            <td>integer</td>
                                            <td>Page number</td>
                                            <td>1</td>
                                        </tr>
                                        <tr>
                                            <td><code>per_page</code></td>
                                            <td>integer</td>
                                            <td>Items per page (max: 100)</td>
                                            <td>20</td>
                                        </tr>
                                        <tr>
                                            <td><code>category</code></td>
                                            <td>string</td>
                                            <td>Filter by category slug</td>
                                            <td>-</td>
                                        </tr>
                                        <tr>
                                            <td><code>sort</code></td>
                                            <td>string</td>
                                            <td>Sort field: created_at, updated_at, downloads, rating</td>
                                            <td>created_at</td>
                                        </tr>
                                        <tr>
                                            <td><code>order</code></td>
                                            <td>string</td>
                                            <td>Sort order: asc, desc</td>
                                            <td>desc</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "Romance Mod Example",
      "slug": "romance-mod-example",
      "description": "A romantic mod with this tag",
      "version": "1.0.0",
      "downloads": 500,
      "rating": 4.5,
      "status": "approved",
      "thumbnail_url": "https://example.com/thumbnails/mod-123.jpg",
      "created_at": "2024-01-10T08:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "author": {
        "id": 456,
        "username": "modder123",
        "display_name": "Awesome Modder"
      },
      "category": {
        "id": 1,
        "name": "Full Mods",
        "slug": "full-mods"
      },
      "tags": ["romance", "comedy", "original-characters"]
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 25,
    "total_pages": 2,
    "has_next": true,
    "has_prev": false
  },
  "tag": {
    "name": "romance",
    "slug": "romance",
    "usage_count": 25
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code># Get all romance mods
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/tags/romance/mods"

# Get romance full mods, sorted by rating
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/tags/romance/mods?category=full-mods&sort=rating&order=desc"</code></pre>
                                </div>
                            </div>

                            <!-- Get Platform Stats -->
                            <div class="endpoint">
                                <h3 id="get-platform-stats">
                                    <span class="method get">GET</span>
                                    <code>/api/stats</code>
                                </h3>
                                <p>Get public platform statistics including category and tag information.</p>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "mods": {
      "total": 150,
      "by_category": {
        "full-mods": 45,
        "mini-mods": 23,
        "tools-utilities": 12,
        "assets": 18
      }
    },
    "downloads": {
      "total": 25000,
      "this_month": 3500
    },
    "users": {
      "total": 1250,
      "active_this_week": 320
    },
    "popular_tags": [
      {
        "name": "romance",
        "usage_count": 25
      },
      {
        "name": "comedy",
        "usage_count": 18
      },
      {
        "name": "drama",
        "usage_count": 15
      }
    ]
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/stats"</code></pre>
                                </div>
                            </div>

                            <!-- Contact -->
                            <div class="endpoint">
                                <h3 id="contact">
                                    <span class="method post">POST</span>
                                    <code>/api/contact</code>
                                </h3>
                                <p>Send a contact message to the platform administrators.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Content-Type: application/json</code></pre>
                                </div>

                                <h4>Request Body</h4>
                                <div class="code-block">
                                    <pre><code>{
  "name": "string",
  "email": "string",
  "subject": "string",
  "message": "string"
}</code></pre>
                                </div>

                                <h4>Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>name</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Sender's name</td>
                                        </tr>
                                        <tr>
                                            <td><code>email</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Sender's email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>subject</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Message subject</td>
                                        </tr>
                                        <tr>
                                            <td><code>message</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Message content</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "message": "Contact message sent successfully",
    "ticket_id": "CONTACT-2024-001",
    "sent_at": "2024-01-15T12:00:00Z"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X POST "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/contact" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "subject": "Question about API",
    "message": "I have a question about the API documentation..."
  }'</code></pre>
                                </div>
                            </div>

                            <!-- Common Error Responses -->
                            <h3>Common Error Responses</h3>

                            <h4>Category Not Found (404)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "CATEGORY_NOT_FOUND",
    "message": "Category not found"
  }
}</code></pre>
                            </div>

                            <h4>Tag Not Found (404)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "TAG_NOT_FOUND",
    "message": "Tag not found"
  }
}</code></pre>
                            </div>

                            <!-- Next Steps -->
                            <div class="next-steps">
                                <h3>Next Steps</h3>
                                <div class="link-grid">
                                    <a href="/docs/api/endpoints/mods" class="quick-link">
                                        <i class="fas fa-puzzle-piece"></i>
                                        <div>
                                            <h4>Mod Endpoints</h4>
                                            <p>Browse and manage mods</p>
                                        </div>
                                    </a>
                                    <a href="/docs/examples" class="quick-link">
                                        <i class="fas fa-code"></i>
                                        <div>
                                            <h4>Code Examples</h4>
                                            <p>See practical implementation examples</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<script src="/js/docs.js"></script>
<%- include('../../../partials/footer') %>