<%- include('../../../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../../../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Authentication Endpoints -->
                    <section class="docs-section" id="auth-endpoints">
                        <h2>Authentication Endpoints</h2>
                        <div class="section-content">
                            <p class="lead">
                                Authentication endpoints handle user registration, login, logout, and OAuth integration.
                                These endpoints manage JWT tokens and user sessions.
                            </p>

                            <!-- Register -->
                            <div class="endpoint">
                                <h3 id="register">
                                    <span class="method post">POST</span>
                                    <code>/api/auth/register</code>
                                </h3>
                                <p>Register a new user account.</p>

                                <h4>Request Body</h4>
                                <div class="code-block">
                                    <pre><code>{
  "username": "string",
  "email": "string",
  "password": "string",
  "display_name": "string" // optional
}</code></pre>
                                </div>

                                <h4>Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>username</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Unique username (3-20 characters, alphanumeric + underscore)</td>
                                        </tr>
                                        <tr>
                                            <td><code>email</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Valid email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>password</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Password (minimum 8 characters)</td>
                                        </tr>
                                        <tr>
                                            <td><code>display_name</code></td>
                                            <td>string</td>
                                            <td>❌ No</td>
                                            <td>Display name (defaults to username)</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "user": {
      "id": 123,
      "username": "newuser",
      "email": "<EMAIL>",
      "display_name": "New User",
      "role": "user",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X POST "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "display_name": "New User"
  }'</code></pre>
                                </div>
                            </div>

                            <!-- Login -->
                            <div class="endpoint">
                                <h3 id="login">
                                    <span class="method post">POST</span>
                                    <code>/api/auth/login</code>
                                </h3>
                                <p>Login with email and password to receive a JWT token.</p>

                                <h4>Request Body</h4>
                                <div class="code-block">
                                    <pre><code>{
  "email": "string",
  "password": "string",
  "remember_me": "boolean" // optional
}</code></pre>
                                </div>

                                <h4>Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>email</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>User's email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>password</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>User's password</td>
                                        </tr>
                                        <tr>
                                            <td><code>remember_me</code></td>
                                            <td>boolean</td>
                                            <td>❌ No</td>
                                            <td>Extend token expiration (default: false)</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "user": {
      "id": 123,
      "username": "existinguser",
      "email": "<EMAIL>",
      "display_name": "Existing User",
      "role": "user",
      "avatar_url": "https://example.com/avatars/user.jpg"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-16T10:30:00Z"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X POST "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "remember_me": true
  }'</code></pre>
                                </div>
                            </div>

                            <!-- Logout -->
                            <div class="endpoint">
                                <h3 id="logout">
                                    <span class="method post">POST</span>
                                    <code>/api/auth/logout</code>
                                </h3>
                                <p>Logout and invalidate the current JWT token.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>
                                </div>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "message": "Successfully logged out"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X POST "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/auth/logout" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- Refresh Token -->
                            <div class="endpoint">
                                <h3 id="refresh">
                                    <span class="method post">POST</span>
                                    <code>/api/auth/refresh</code>
                                </h3>
                                <p>Refresh an expired or soon-to-expire JWT token.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>
                                </div>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-16T10:30:00Z"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X POST "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/auth/refresh" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- GitHub OAuth -->
                            <div class="endpoint">
                                <h3 id="github-oauth">
                                    <span class="method get">GET</span>
                                    <code>/api/auth/github</code>
                                </h3>
                                <p>Initiate GitHub OAuth authentication flow.</p>

                                <h4>Query Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>redirect_uri</code></td>
                                            <td>string</td>
                                            <td>❌ No</td>
                                            <td>URL to redirect after authentication</td>
                                        </tr>
                                        <tr>
                                            <td><code>state</code></td>
                                            <td>string</td>
                                            <td>❌ No</td>
                                            <td>State parameter for security</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <p>Redirects to GitHub OAuth authorization page.</p>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code># Direct browser to this URL
<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/auth/github?redirect_uri=https://yourapp.com/callback</code></pre>
                                </div>
                            </div>



                            <!-- Error Responses -->
                            <h3>Common Error Responses</h3>

                            <h4>Invalid Credentials (401)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid email or password"
  }
}</code></pre>
                            </div>

                            <h4>User Already Exists (409)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "USER_ALREADY_EXISTS",
    "message": "A user with this email or username already exists",
    "details": {
      "field": "email"
    }
  }
}</code></pre>
                            </div>

                            <h4>Validation Error (422)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "fields": {
        "email": "Invalid email format",
        "password": "Password must be at least 8 characters"
      }
    }
  }
}</code></pre>
                            </div>

                            <!-- Rate Limits -->
                            <h3>Rate Limits</h3>
                            <p>Authentication endpoints have specific rate limits to prevent abuse:</p>

                            <table class="docs-table">
                                <thead>
                                    <tr>
                                        <th>Endpoint</th>
                                        <th>Rate Limit</th>
                                        <th>Window</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>POST /auth/register</code></td>
                                        <td>3 requests</td>
                                        <td>Per hour per IP</td>
                                    </tr>
                                    <tr>
                                        <td><code>POST /auth/login</code></td>
                                        <td>10 requests</td>
                                        <td>Per hour per IP</td>
                                    </tr>
                                    <tr>
                                        <td><code>POST /auth/logout</code></td>
                                        <td>No limit</td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td><code>POST /auth/refresh</code></td>
                                        <td>60 requests</td>
                                        <td>Per hour per user</td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Next Steps -->
                            <div class="next-steps">
                                <h3>Next Steps</h3>
                                <div class="link-grid">
                                    <a href="/docs/api/endpoints/mods" class="quick-link">
                                        <i class="fas fa-puzzle-piece"></i>
                                        <div>
                                            <h4>Mod Endpoints</h4>
                                            <p>Browse and manage mods</p>
                                        </div>
                                    </a>

                                    <a href="/docs/api/endpoints/users" class="quick-link">
                                        <i class="fas fa-users"></i>
                                        <div>
                                            <h4>User Endpoints</h4>
                                            <p>Manage user profiles</p>
                                        </div>
                                    </a>


                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../../../partials/footer') %>
