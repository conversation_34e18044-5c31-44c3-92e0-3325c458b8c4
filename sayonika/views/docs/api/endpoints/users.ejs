<%- include('../../../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../../../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- User Endpoints -->
                    <section class="docs-section" id="user-endpoints">
                        <h2>User Endpoints</h2>
                        <div class="section-content">
                            <p class="lead">
                                User endpoints allow you to manage user profiles, view public user information,
                                and handle user-specific operations like profile updates and mod management.
                            </p>

                            <!-- Get Current User -->
                            <div class="endpoint">
                                <h3 id="get-current-user">
                                    <span class="method get">GET</span>
                                    <code>/api/user/me</code>
                                </h3>
                                <p>Get the current authenticated user's profile information.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>
                                </div>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "id": 123,
    "username": "myusername",
    "email": "<EMAIL>",
    "display_name": "My Display Name",
    "role": "user",
    "avatar_url": "https://example.com/avatars/user-123.jpg",
    "bio": "A passionate DDLC modder",
    "created_at": "2024-01-10T08:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "stats": {
      "mods_uploaded": 5,
      "total_downloads": 1500,
      "total_reviews": 25
    },
    "oauth_accounts": {
      "github": {
        "connected": true,
        "username": "myusername"
      }
    }
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/me" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- Get Public User -->
                            <div class="endpoint">
                                <h3 id="get-public-user">
                                    <span class="method get">GET</span>
                                    <code>/api/users/{username}</code>
                                </h3>
                                <p>Get public information about a specific user.</p>

                                <h4>Path Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>username</code></td>
                                            <td>string</td>
                                            <td>User's username</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "id": 456,
    "username": "modder123",
    "display_name": "Awesome Modder",
    "role": "user",
    "avatar_url": "https://example.com/avatars/user-456.jpg",
    "bio": "Creating amazing DDLC experiences",
    "created_at": "2024-01-01T00:00:00Z",
    "stats": {
      "mods_uploaded": 12,
      "total_downloads": 5000,
      "total_reviews": 150
    }
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/users/modder123"</code></pre>
                                </div>
                            </div>

                            <!-- Update Profile -->
                            <div class="endpoint">
                                <h3 id="update-profile">
                                    <span class="method put">PUT</span>
                                    <code>/api/user/me</code>
                                </h3>
                                <p>Update the current user's profile information.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json</code></pre>
                                </div>

                                <h4>Request Body</h4>
                                <div class="code-block">
                                    <pre><code>{
  "display_name": "string",  // optional
  "bio": "string",          // optional
  "email": "string"         // optional
}</code></pre>
                                </div>

                                <h4>Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>display_name</code></td>
                                            <td>string</td>
                                            <td>❌ No</td>
                                            <td>Display name (3-50 characters)</td>
                                        </tr>
                                        <tr>
                                            <td><code>bio</code></td>
                                            <td>string</td>
                                            <td>❌ No</td>
                                            <td>User biography (max 500 characters)</td>
                                        </tr>
                                        <tr>
                                            <td><code>email</code></td>
                                            <td>string</td>
                                            <td>❌ No</td>
                                            <td>Email address</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "id": 123,
    "username": "myusername",
    "email": "<EMAIL>",
    "display_name": "New Display Name",
    "bio": "Updated biography",
    "updated_at": "2024-01-15T12:00:00Z"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X PUT "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/me" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "display_name": "New Display Name",
    "bio": "Updated biography"
  }'</code></pre>
                                </div>
                            </div>

                            <!-- Upload Avatar -->
                            <div class="endpoint">
                                <h3 id="upload-avatar">
                                    <span class="method post">POST</span>
                                    <code>/api/user/avatar</code>
                                </h3>
                                <p>Upload or update the user's profile avatar.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: multipart/form-data</code></pre>
                                </div>

                                <h4>Form Data</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>avatar</code></td>
                                            <td>file</td>
                                            <td>✅ Yes</td>
                                            <td>Image file (JPG/PNG, max 5MB, min 100x100px)</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "avatar_url": "https://example.com/avatars/user-123-new.jpg",
    "message": "Avatar updated successfully"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X POST "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/avatar" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "avatar=@my-avatar.jpg"</code></pre>
                                </div>
                            </div>

                            <!-- Remove Avatar -->
                            <div class="endpoint">
                                <h3 id="remove-avatar">
                                    <span class="method delete">DELETE</span>
                                    <code>/api/user/avatar</code>
                                </h3>
                                <p>Remove the user's profile avatar and revert to default.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>
                                </div>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "avatar_url": "https://example.com/avatars/default-123.jpg",
    "message": "Avatar removed successfully"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X DELETE "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/avatar" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- Get User Mods -->
                            <div class="endpoint">
                                <h3 id="get-user-mods">
                                    <span class="method get">GET</span>
                                    <code>/api/users/{username}/mods</code>
                                </h3>
                                <p>Get a list of mods uploaded by a specific user.</p>

                                <h4>Path Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>username</code></td>
                                            <td>string</td>
                                            <td>User's username</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Query Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Default</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>page</code></td>
                                            <td>integer</td>
                                            <td>Page number</td>
                                            <td>1</td>
                                        </tr>
                                        <tr>
                                            <td><code>per_page</code></td>
                                            <td>integer</td>
                                            <td>Items per page (max: 50)</td>
                                            <td>20</td>
                                        </tr>
                                        <tr>
                                            <td><code>status</code></td>
                                            <td>string</td>
                                            <td>Filter by status: approved, pending, rejected</td>
                                            <td>approved</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "User's Awesome Mod",
      "slug": "users-awesome-mod",
      "description": "A great mod by this user",
      "version": "1.0.0",
      "downloads": 500,
      "rating": 4.5,
      "status": "approved",
      "thumbnail_url": "https://example.com/thumbnails/mod-123.jpg",
      "created_at": "2024-01-10T08:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "category": {
        "id": 1,
        "name": "Full Mods",
        "slug": "full-mods"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 5,
    "total_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code># Get all approved mods by user
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/users/modder123/mods"

# Get pending mods (requires authentication and ownership)
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/users/modder123/mods?status=pending" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- Change Password -->
                            <div class="endpoint">
                                <h3 id="change-password">
                                    <span class="method put">PUT</span>
                                    <code>/api/user/password</code>
                                </h3>
                                <p>Change the current user's password.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json</code></pre>
                                </div>

                                <h4>Request Body</h4>
                                <div class="code-block">
                                    <pre><code>{
  "current_password": "string",
  "new_password": "string"
}</code></pre>
                                </div>

                                <h4>Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Field</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>current_password</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>Current password for verification</td>
                                        </tr>
                                        <tr>
                                            <td><code>new_password</code></td>
                                            <td>string</td>
                                            <td>✅ Yes</td>
                                            <td>New password (minimum 8 characters)</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "message": "Password updated successfully"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X PUT "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "current_password": "oldpassword123",
    "new_password": "newpassword456"
  }'</code></pre>
                                </div>
                            </div>

                            <!-- OAuth Account Management -->
                            <div class="endpoint">
                                <h3 id="oauth-accounts">
                                    <span class="method get">GET</span>
                                    <code>/api/user/oauth</code>
                                </h3>
                                <p>Get connected OAuth accounts for the current user.</p>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>
                                </div>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "github": {
      "connected": true,
      "username": "myusername",
      "connected_at": "2024-01-10T08:00:00Z"
    }
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/oauth" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- Unlink OAuth Account -->
                            <div class="endpoint">
                                <h3 id="unlink-oauth">
                                    <span class="method delete">DELETE</span>
                                    <code>/api/user/oauth/{provider}</code>
                                </h3>
                                <p>Unlink an OAuth account from the current user.</p>

                                <h4>Path Parameters</h4>
                                <table class="docs-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>provider</code></td>
                                            <td>string</td>
                                            <td>OAuth provider (github)</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h4>Headers</h4>
                                <div class="code-block">
                                    <pre><code>Authorization: Bearer YOUR_JWT_TOKEN</code></pre>
                                </div>

                                <h4>Response</h4>
                                <div class="code-block">
                                    <pre><code>{
  "success": true,
  "data": {
    "message": "GitHub account unlinked successfully"
  }
}</code></pre>
                                </div>

                                <h4>Example</h4>
                                <div class="code-block">
                                    <pre><code>curl -X DELETE "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/user/oauth/github" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>

                            <!-- Common Error Responses -->
                            <h3>Common Error Responses</h3>

                            <h4>User Not Found (404)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
}</code></pre>
                            </div>

                            <h4>Unauthorized (401)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  }
}</code></pre>
                            </div>

                            <h4>Validation Error (422)</h4>
                            <div class="code-block">
                                <pre><code>{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "fields": {
        "email": "Invalid email format",
        "bio": "Bio must be less than 500 characters"
      }
    }
  }
}</code></pre>
                            </div>

                            <!-- Next Steps -->
                            <div class="next-steps">
                                <h3>Next Steps</h3>
                                <div class="link-grid">
                                    <a href="/docs/api/endpoints/admin" class="quick-link">
                                        <i class="fas fa-shield-alt"></i>
                                        <div>
                                            <h4>Admin Endpoints</h4>
                                            <p>Administrative functions</p>
                                        </div>
                                    </a>
                                    <a href="/docs/api/endpoints/categories" class="quick-link">
                                        <i class="fas fa-tags"></i>
                                        <div>
                                            <h4>Category Endpoints</h4>
                                            <p>Browse categories and tags</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<script src="/js/docs.js"></script>
<%- include('../../../partials/footer') %>