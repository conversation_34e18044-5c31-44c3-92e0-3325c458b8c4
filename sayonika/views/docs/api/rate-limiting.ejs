<%- include('../../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Rate Limiting Guide -->
                    <section class="docs-section" id="rate-limiting">
                        <h2>Rate Limiting</h2>
                        <div class="section-content">
                            <p class="lead">
                                To ensure fair usage and maintain service quality, the Sayonika API implements rate limiting.
                                This guide explains how rate limits work and how to handle them in your applications.
                            </p>

                            <!-- Rate Limit Overview -->
                            <h3>Rate Limit Overview</h3>
                            <p>
                                Rate limits are applied per IP address for anonymous requests and per user for authenticated requests.
                                The Sayonika API uses a sliding window approach to ensure fair usage and maintain service quality.
                            </p>

                            <div class="alert info">
                                <i class="fas fa-info-circle"></i>
                                <div>
                                    <strong>Default Limits:</strong> The API currently applies a default rate limit of 100 requests per 15-minute window
                                    for most endpoints, with higher limits for authenticated users.
                                </div>
                            </div>

                            <!-- Rate Limit Tiers -->
                            <h3>Rate Limit Tiers</h3>

                            <table class="docs-table">
                                <thead>
                                    <tr>
                                        <th>User Type</th>
                                        <th>Requests per 15 min</th>
                                        <th>Requests per Hour</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge info">Anonymous</span></td>
                                        <td>100</td>
                                        <td>400</td>
                                        <td>Unauthenticated requests (per IP)</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge success">Authenticated</span></td>
                                        <td>300</td>
                                        <td>1,200</td>
                                        <td>Logged-in users</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge danger">Admin</span></td>
                                        <td>1,000</td>
                                        <td>4,000</td>
                                        <td>Administrative users</td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Rate Limit Headers -->
                            <h3>Rate Limit Headers</h3>
                            <p>Every API response includes headers that provide information about your current rate limit status:</p>

                            <table class="docs-table">
                                <thead>
                                    <tr>
                                        <th>Header</th>
                                        <th>Description</th>
                                        <th>Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>X-RateLimit-Limit</code></td>
                                        <td>Maximum requests allowed in current window</td>
                                        <td><code>300</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>X-RateLimit-Remaining</code></td>
                                        <td>Requests remaining in current window</td>
                                        <td><code>299</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>X-RateLimit-Reset</code></td>
                                        <td>Unix timestamp when window resets</td>
                                        <td><code>1640995200</code></td>
                                    </tr>
                                    <tr>
                                        <td><code>X-RateLimit-Window</code></td>
                                        <td>Window duration in seconds</td>
                                        <td><code>900</code> (15 minutes)</td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Example Response -->
                            <h4>Example Response Headers</h4>
                            <div class="code-block">
                                <pre><code>HTTP/1.1 200 OK
Content-Type: application/json
X-RateLimit-Limit: 300
X-RateLimit-Remaining: 299
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 900

{
  "success": true,
  "data": { ... }
}</code></pre>
                            </div>

                            <!-- Rate Limit Exceeded -->
                            <h3>Rate Limit Exceeded</h3>
                            <p>When you exceed the rate limit, the API will return a <code>429 Too Many Requests</code> status code:</p>

                            <div class="code-block">
                                <pre><code>HTTP/1.1 429 Too Many Requests
Content-Type: application/json
X-RateLimit-Limit: 300
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 900
Retry-After: 900

{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please try again later.",
    "details": {
      "limit": 300,
      "window": 900,
      "reset_at": "2024-01-15T11:30:00Z"
    }
  }
}</code></pre>
                            </div>

                            <!-- Handling Rate Limits -->
                            <h3>Handling Rate Limits</h3>

                            <h4>Best Practices</h4>
                            <ul>
                                <li><strong>Monitor Headers:</strong> Always check rate limit headers in responses</li>
                                <li><strong>Implement Backoff:</strong> Use exponential backoff when rate limited</li>
                                <li><strong>Cache Responses:</strong> Cache API responses to reduce unnecessary requests</li>
                                <li><strong>Batch Requests:</strong> Combine multiple operations into single requests when possible</li>
                            </ul>

                            <!-- Code Examples -->
                            <h3>Code Examples</h3>

                            <div class="language-tabs">
                                <div class="tab-buttons">
                                    <button class="tab-button active" data-tab="js-rate">JavaScript</button>
                                    <button class="tab-button" data-tab="python-rate">Python</button>
                                    <button class="tab-button" data-tab="curl-rate">cURL</button>
                                </div>

                                <div class="tab-content active" id="js-rate">
                                    <div class="code-block">
                                        <pre><code>class SayonikaAPI {
  constructor(baseURL, token = null) {
    this.baseURL = baseURL;
    this.token = token;
    this.rateLimitInfo = {
      limit: null,
      remaining: null,
      reset: null,
      window: null
    };
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add auth header if token is available
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, { ...options, headers });

      // Update rate limit info from headers
      this.updateRateLimitInfo(response.headers);

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = parseInt(response.headers.get('Retry-After') || '900');
        console.warn(`Rate limit exceeded. Retrying after ${retryAfter} seconds...`);

        // Wait and retry once
        await this.sleep(retryAfter * 1000);
        return this.request(endpoint, options);
      }

      // Parse response
      const data = await response.json();

      if (!response.ok) {
        throw new APIError(data.error || { message: 'Request failed' }, response.status);
      }

      return data;
    } catch (error) {
      if (error instanceof APIError && error.statusCode === 429) {
        // Already handled above, but just in case
        throw error;
      }
      throw error;
    }
  }

  updateRateLimitInfo(headers) {
    this.rateLimitInfo = {
      limit: parseInt(headers.get('X-RateLimit-Limit')) || null,
      remaining: parseInt(headers.get('X-RateLimit-Remaining')) || null,
      reset: parseInt(headers.get('X-RateLimit-Reset')) || null,
      window: parseInt(headers.get('X-RateLimit-Window')) || null
    };
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getRateLimitStatus() {
    return {
      ...this.rateLimitInfo,
      resetTime: this.rateLimitInfo.reset ? new Date(this.rateLimitInfo.reset * 1000) : null,
      windowMinutes: this.rateLimitInfo.window ? Math.floor(this.rateLimitInfo.window / 60) : null
    };
  }

  // Helper methods for common operations
  async getMods(params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request(`/mods${query ? '?' + query : ''}`);
  }

  async getMod(id) {
    return this.request(`/mods/${id}`);
  }

  async uploadMod(modData) {
    return this.request('/mods', {
      method: 'POST',
      body: JSON.stringify(modData)
    });
  }
}

class APIError extends Error {
  constructor(errorData, statusCode) {
    super(errorData.message || 'API Error');
    this.code = errorData.code;
    this.details = errorData.details;
    this.statusCode = statusCode;
  }
}

// Usage example
const api = new SayonikaAPI('https://your-domain.com/api', 'your_jwt_token');

// Check rate limit status
console.log('Rate limit status:', api.getRateLimitStatus());

// Make requests with automatic rate limit handling
try {
  const mods = await api.getMods({ page: 1, limit: 20 });
  console.log('Fetched mods:', mods.data.length);
} catch (error) {
  console.error('API Error:', error.message);
}</code></pre>
                                    </div>
                                </div>

                                <div class="tab-content" id="python-rate">
                                    <div class="code-block">
                                        <pre><code>import requests
import time
from datetime import datetime
from typing import Optional, Dict, Any

class SayonikaAPI:
    def __init__(self, base_url: str, token: Optional[str] = None):
        self.base_url = base_url
        self.token = token
        self.rate_limit_info = {
            'limit': None,
            'remaining': None,
            'reset': None,
            'window': None
        }

    def request(self, endpoint: str, method: str = 'GET', **kwargs) -> Dict[Any, Any]:
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Content-Type': 'application/json'
        }

        # Add auth header if token is available
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'

        headers.update(kwargs.get('headers', {}))
        kwargs['headers'] = headers

        try:
            response = requests.request(method, url, **kwargs)

            # Update rate limit info
            self._update_rate_limit_info(response.headers)

            # Handle rate limiting
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 900))
                print(f"Rate limit exceeded. Waiting {retry_after} seconds...")
                time.sleep(retry_after)
                return self.request(endpoint, method, **kwargs)

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            raise

    def _update_rate_limit_info(self, headers):
        self.rate_limit_info = {
            'limit': int(headers.get('X-RateLimit-Limit', 0)) or None,
            'remaining': int(headers.get('X-RateLimit-Remaining', 0)) or None,
            'reset': int(headers.get('X-RateLimit-Reset', 0)) or None,
            'window': int(headers.get('X-RateLimit-Window', 0)) or None
        }

    def get_rate_limit_status(self) -> Dict[str, Any]:
        return {
            **self.rate_limit_info,
            'reset_time': datetime.fromtimestamp(self.rate_limit_info['reset']) if self.rate_limit_info['reset'] else None,
            'window_minutes': self.rate_limit_info['window'] // 60 if self.rate_limit_info['window'] else None
        }

    # Helper methods for common operations
    def get_mods(self, **params) -> Dict[Any, Any]:
        return self.request('/mods', params=params)

    def get_mod(self, mod_id: str) -> Dict[Any, Any]:
        return self.request(f'/mods/{mod_id}')

    def upload_mod(self, mod_data: Dict[Any, Any]) -> Dict[Any, Any]:
        return self.request('/mods', method='POST', json=mod_data)

# Usage example
api = SayonikaAPI('https://your-domain.com/api', 'your_jwt_token')

# Check rate limit status
print('Rate limit status:', api.get_rate_limit_status())

# Make requests with automatic rate limit handling
try:
    mods = api.get_mods(page=1, limit=20)
    print(f'Fetched {len(mods["data"])} mods')
    print(f'Current rate limit: {api.get_rate_limit_status()}')
except Exception as e:
    print(f'API Error: {e}')</code></pre>
                                    </div>
                                </div>

                                <div class="tab-content" id="curl-rate">
                                    <div class="code-block">
                                        <pre><code>#!/bin/bash

# Configuration
BASE_URL="https://your-domain.com/api"
TOKEN="your_jwt_token"

# Function to make API request with rate limit handling
make_api_request() {
    local endpoint=$1
    local method=${2:-GET}
    local data=${3:-}

    local curl_args=(-s -w "\n%{http_code}\n")
    curl_args+=(-H "Content-Type: application/json")

    # Add auth header if token is set
    if [ -n "$TOKEN" ]; then
        curl_args+=(-H "Authorization: Bearer $TOKEN")
    fi

    # Add method and data if specified
    if [ "$method" != "GET" ]; then
        curl_args+=(-X "$method")
    fi

    if [ -n "$data" ]; then
        curl_args+=(-d "$data")
    fi

    # Make the request
    local response=$(curl "${curl_args[@]}" "$BASE_URL$endpoint")
    local body=$(echo "$response" | head -n -1)
    local status_code=$(echo "$response" | tail -n 1)

    # Check for rate limiting
    if [ "$status_code" = "429" ]; then
        echo "Rate limit exceeded. Waiting 15 minutes..." >&2
        sleep 900  # Wait 15 minutes
        make_api_request "$endpoint" "$method" "$data"
    elif [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo "$body"
    else
        echo "Request failed with status $status_code:" >&2
        echo "$body" >&2
        return 1
    fi
}

# Function to check rate limit status
check_rate_limit() {
    local response=$(curl -s -I \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/mods")

    local limit=$(echo "$response" | grep -i "x-ratelimit-limit" | cut -d: -f2 | tr -d ' \r')
    local remaining=$(echo "$response" | grep -i "x-ratelimit-remaining" | cut -d: -f2 | tr -d ' \r')
    local reset=$(echo "$response" | grep -i "x-ratelimit-reset" | cut -d: -f2 | tr -d ' \r')

    echo "Rate Limit Status:"
    echo "  Limit: $limit"
    echo "  Remaining: $remaining"
    echo "  Resets at: $(date -d @$reset 2>/dev/null || echo $reset)"
}

# Usage examples
echo "Checking rate limit status..."
check_rate_limit

echo -e "\nFetching mods..."
make_api_request "/mods?page=1&limit=10"

echo -e "\nFetching specific mod..."
make_api_request "/mods/example-mod"</code></pre>
                                    </div>
                                </div>
                            </div>

                            <!-- Endpoint-Specific Limits -->
                            <h3>Endpoint-Specific Limits</h3>
                            <p>Some endpoints have additional rate limits due to their resource-intensive nature or security considerations:</p>

                            <table class="docs-table">
                                <thead>
                                    <tr>
                                        <th>Endpoint Pattern</th>
                                        <th>Additional Limit</th>
                                        <th>Window</th>
                                        <th>Reason</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>POST /api/mods</code></td>
                                        <td>3 uploads</td>
                                        <td>1 hour</td>
                                        <td>Prevent spam uploads</td>
                                    </tr>
                                    <tr>
                                        <td><code>POST /api/auth/login</code></td>
                                        <td>5 attempts</td>
                                        <td>15 minutes</td>
                                        <td>Prevent brute force attacks</td>
                                    </tr>
                                    <tr>
                                        <td><code>POST /api/contact</code></td>
                                        <td>2 messages</td>
                                        <td>1 hour</td>
                                        <td>Prevent spam messages</td>
                                    </tr>
                                    <tr>
                                        <td><code>GET /api/mods/*/download</code></td>
                                        <td>10 downloads</td>
                                        <td>15 minutes</td>
                                        <td>Bandwidth protection</td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- Increasing Rate Limits -->
                            <h3>Increasing Rate Limits</h3>
                            <div class="alert info">
                                <i class="fas fa-info-circle"></i>
                                <div>
                                    <strong>Need Higher Limits?</strong> If you need higher rate limits for your application,
                                    please <a href="https://github.com/Dynamicaaa/Sayonika/issues/new" target="_blank">contact us</a>
                                    with details about your use case.
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../../partials/footer') %>
