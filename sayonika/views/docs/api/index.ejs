<%- include('../../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- API Overview -->
                    <section class="docs-section" id="api-overview">
                        <h2>API Reference</h2>
                        <div class="section-content">
                            <p class="lead">
                                Sayonika provides a comprehensive REST API for managing mods, users, and platform data.
                                Our API is designed to be intuitive, well-documented, and easy to integrate.
                            </p>

                            <!-- Quick Start -->
                            <div class="alert info">
                                <i class="fas fa-rocket"></i>
                                <div>
                                    <strong>Quick Start:</strong> All API endpoints are available at <code>https://your-domain.com/api/</code>
                                    and require proper authentication for protected resources.
                                </div>
                            </div>

                            <!-- API Features -->
        <div class="feature-grid">
            <div class="feature-item">
                <h4><i class="fas fa-shield-alt"></i> Secure Authentication</h4>
                <p>JWT-based authentication with OAuth support for GitHub and Discord integration, plus account linking.</p>
            </div>

            <div class="feature-item">
                <h4><i class="fas fa-tachometer-alt"></i> Rate Limiting</h4>
                <p>Built-in rate limiting to ensure fair usage and protect against abuse with configurable limits.</p>
            </div>

            <div class="feature-item">
                <h4><i class="fas fa-code"></i> RESTful Design</h4>
                <p>Clean, predictable REST endpoints following industry best practices with comprehensive documentation.</p>
            </div>

            <div class="feature-item">
                <h4><i class="fas fa-file-code"></i> JSON Responses</h4>
                <p>Consistent JSON response format with detailed error messages and status codes.</p>
            </div>

            <div class="feature-item">
                <h4><i class="fas fa-users"></i> User Management</h4>
                <p>Complete user profile management including avatars, achievements, and OAuth account linking.</p>
            </div>

            <div class="feature-item">
                <h4><i class="fas fa-comments"></i> Community Features</h4>
                <p>Mod comments, ratings, achievements system, and user interaction endpoints.</p>
            </div>
        </div>

        <!-- Base URL -->
        <div class="alert info">
            <i class="fas fa-info-circle"></i>
            <div>
                <strong>Base URL:</strong> All API endpoints are relative to <code><%- process.env.BASE_URL || 'http://localhost:3000' %>/api</code>
            </div>
        </div>

        <!-- Quick Start -->
        <h3>Quick Start</h3>
        <p>Here's a simple example to get you started with the Sayonika API:</p>

        <div class="language-tabs">
            <div class="tab-buttons">
                <button class="tab-button" data-tab="curl-example">cURL</button>
                <button class="tab-button" data-tab="js-example">JavaScript</button>
                <button class="tab-button" data-tab="python-example">Python</button>
            </div>

            <div class="tab-content" id="curl-example">
                <div class="code-block">
                    <pre><code># Get all public mods
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/mods" \
  -H "Accept: application/json"

# Get a specific mod
curl -X GET "<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/mods/example-mod" \
  -H "Accept: application/json"</code></pre>
                </div>
            </div>

            <div class="tab-content" id="js-example">
                <div class="code-block">
                    <pre><code>// Using fetch API
const response = await fetch('<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/mods');
const mods = await response.json();

console.log('Available mods:', mods);

// Get specific mod
const modResponse = await fetch('<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/mods/example-mod');
const mod = await modResponse.json();

console.log('Mod details:', mod);</code></pre>
                </div>
            </div>

            <div class="tab-content" id="python-example">
                <div class="code-block">
                    <pre><code>import requests

# Get all mods
response = requests.get('<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/mods')
mods = response.json()

print('Available mods:', mods)

# Get specific mod
mod_response = requests.get('<%- process.env.BASE_URL || 'http://localhost:3000' %>/api/mods/example-mod')
mod = mod_response.json()

print('Mod details:', mod)</code></pre>
                </div>
            </div>
        </div>

        <!-- API Sections -->
        <h3>API Sections</h3>
        <div class="quick-links">
            <div class="link-grid">
                <a href="/docs/api/authentication" class="quick-link">
                    <i class="fas fa-key"></i>
                    <div>
                        <h4>Authentication</h4>
                        <p>Learn how to authenticate with the API</p>
                    </div>
                </a>

                <a href="/docs/api/rate-limiting" class="quick-link">
                    <i class="fas fa-clock"></i>
                    <div>
                        <h4>Rate Limiting</h4>
                        <p>Understand API rate limits and headers</p>
                    </div>
                </a>

                <a href="/docs/api/response-format" class="quick-link">
                    <i class="fas fa-file-alt"></i>
                    <div>
                        <h4>Response Format</h4>
                        <p>Standard response structure and formats</p>
                    </div>
                </a>

                <a href="/docs/api/error-handling" class="quick-link">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <h4>Error Handling</h4>
                        <p>How to handle API errors gracefully</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Endpoints Overview -->
        <h3>Available Endpoints</h3>
        <table class="docs-table">
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Description</th>
                    <th>Base Path</th>
                    <th>Authentication</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><a href="/docs/api/endpoints/auth">Authentication</a></td>
                    <td>User login, registration, and OAuth (GitHub/Discord)</td>
                    <td><code>/api/auth</code></td>
                    <td><span class="badge info">Public</span></td>
                </tr>
                <tr>
                    <td><a href="/docs/api/endpoints/mods">Mods</a></td>
                    <td>Mod management, browsing, and comments</td>
                    <td><code>/api/mods</code></td>
                    <td><span class="badge warning">Mixed</span></td>
                </tr>
                <tr>
                    <td><a href="/docs/api/endpoints/users">Users</a></td>
                    <td>User profiles, avatars, and achievements</td>
                    <td><code>/api/users</code></td>
                    <td><span class="badge warning">Mixed</span></td>
                </tr>
                <tr>
                    <td><a href="/docs/api/endpoints/admin">Admin</a></td>
                    <td>Administrative functions and settings</td>
                    <td><code>/api/admin</code></td>
                    <td><span class="badge danger">Admin</span></td>
                </tr>
                <tr>
                    <td><a href="/docs/api/endpoints/categories">Categories</a></td>
                    <td>Mod categories and tags</td>
                    <td><code>/api/categories</code></td>
                    <td><span class="badge info">Public</span></td>
                </tr>
                <tr>
                    <td>OAuth</td>
                    <td>OAuth account linking and management</td>
                    <td><code>/auth/oauth</code></td>
                    <td><span class="badge warning">Mixed</span></td>
                </tr>
                <tr>
                    <td>Achievements</td>
                    <td>User achievements and gamification</td>
                    <td><code>/api/achievements</code></td>
                    <td><span class="badge info">Public</span></td>
                </tr>
            </tbody>
        </table>

        <!-- HTTP Status Codes -->
        <h3>HTTP Status Codes</h3>
        <p>The API uses standard HTTP status codes to indicate the success or failure of requests:</p>

        <table class="docs-table">
            <thead>
                <tr>
                    <th>Code</th>
                    <th>Status</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="status-code success">200</span></td>
                    <td>OK</td>
                    <td>Request successful</td>
                </tr>
                <tr>
                    <td><span class="status-code success">201</span></td>
                    <td>Created</td>
                    <td>Resource created successfully</td>
                </tr>
                <tr>
                    <td><span class="status-code client-error">400</span></td>
                    <td>Bad Request</td>
                    <td>Invalid request parameters</td>
                </tr>
                <tr>
                    <td><span class="status-code client-error">401</span></td>
                    <td>Unauthorized</td>
                    <td>Authentication required</td>
                </tr>
                <tr>
                    <td><span class="status-code client-error">403</span></td>
                    <td>Forbidden</td>
                    <td>Insufficient permissions</td>
                </tr>
                <tr>
                    <td><span class="status-code client-error">404</span></td>
                    <td>Not Found</td>
                    <td>Resource not found</td>
                </tr>
                <tr>
                    <td><span class="status-code client-error">429</span></td>
                    <td>Too Many Requests</td>
                    <td>Rate limit exceeded</td>
                </tr>
                <tr>
                    <td><span class="status-code server-error">500</span></td>
                    <td>Internal Server Error</td>
                    <td>Server error occurred</td>
                </tr>
            </tbody>
        </table>

                            <!-- Getting Help -->
                            <div class="alert success">
                                <i class="fas fa-question-circle"></i>
                                <div>
                                    <strong>Need Help?</strong> If you have questions about the API or need assistance,
                                    feel free to <a href="https://github.com/Dynamicaaa/Sayonika/issues/new" target="_blank">create an issue</a>
                                    on our GitHub repository.
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../../partials/footer') %>
