<%- include('../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Configuration Section -->
                    <section class="docs-section" id="configuration">
                        <h2>Configuration</h2>
                        <div class="section-content">
                            <p class="lead">
                                Sayonika uses environment variables for configuration. This guide covers all available
                                configuration options to customize your instance according to your needs.
                            </p>

                            <!-- Environment File -->
                            <div class="config-section">
                                <h3>Environment File (.env)</h3>
                                <p>All configuration is done through the <code>.env</code> file in your project root. If you haven't created one yet, copy the example file:</p>

                                <div class="code-block">
                                    <div class="code-header">
                                        <span>Create .env file</span>
                                        <button class="copy-btn" data-copy="cp .env.example .env">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <pre><code>cp .env.example .env</code></pre>
                                </div>

                                <div class="alert warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <div>
                                        <strong>Security Note:</strong> Never commit your <code>.env</code> file to version control.
                                        It contains sensitive information like API keys and secrets.
                                    </div>
                                </div>
                            </div>

                            <!-- Basic Configuration -->
                            <div class="config-section">
                                <h3>Basic Configuration</h3>

                                <div class="config-item">
                                    <h4>PORT</h4>
                                    <p>The port number on which the server will run.</p>
                                    <div class="code-block">
                                        <pre><code>PORT=3000</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> 3000</p>
                                </div>

                                <div class="config-item">
                                    <h4>SESSION_SECRET</h4>
                                    <p>A secret key used to sign session cookies. Use a long, random string.</p>
                                    <div class="code-block">
                                        <pre><code>SESSION_SECRET=your-very-long-random-secret-key-here</code></pre>
                                    </div>
                                    <p><strong>Required:</strong> Yes</p>
                                    <div class="alert info">
                                        <i class="fas fa-info-circle"></i>
                                        <div>
                                            Generate a secure secret using: <code>node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"</code>
                                        </div>
                                    </div>
                                </div>

                                <div class="config-item">
                                    <h4>BASE_URL</h4>
                                    <p>The base URL of your Sayonika instance. Used for OAuth redirects and email links.</p>
                                    <div class="code-block">
                                        <pre><code># Development
BASE_URL=http://localhost:3000

# Production
BASE_URL=https://your-domain.com</code></pre>
                                    </div>
                                    <p><strong>Required:</strong> Yes</p>
                                </div>
                            </div>

                            <!-- Database Configuration -->
                            <div class="config-section">
                                <h3>Database Configuration</h3>

                                <div class="config-item">
                                    <h4>DATABASE_PATH</h4>
                                    <p>Path to the SQLite database file.</p>
                                    <div class="code-block">
                                        <pre><code>DATABASE_PATH=./database/sayonika.db</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> ./database/sayonika.db</p>
                                </div>

                                <div class="config-item">
                                    <h4>SESSIONS_DATABASE_PATH</h4>
                                    <p>Path to the sessions database file.</p>
                                    <div class="code-block">
                                        <pre><code>SESSIONS_DATABASE_PATH=./database/sessions.db</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> ./database/sessions.db</p>
                                </div>
                            </div>

                            <!-- OAuth Configuration -->
                            <div class="config-section">
                                <h3>OAuth Configuration</h3>
                                <p>Configure OAuth providers for social login. Both GitHub and Discord are supported.</p>

                                <div class="config-item">
                                    <h4>GitHub OAuth</h4>
                                    <p>To enable GitHub login, create an OAuth app in your GitHub settings and add these variables:</p>
                                    <div class="code-block">
                                        <pre><code>GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=https://your-domain.com/auth/github/callback</code></pre>
                                    </div>
                                    <p><strong>Callback URL:</strong> <code>{BASE_URL}/auth/github/callback</code></p>
                                    <div class="alert info">
                                        <i class="fas fa-info-circle"></i>
                                        <div>
                                            <strong>GitHub Setup:</strong> Go to GitHub Settings → Developer settings → OAuth Apps → New OAuth App.
                                            Set the Authorization callback URL to your BASE_URL + /auth/github/callback.
                                        </div>
                                    </div>
                                </div>

                                <div class="config-item">
                                    <h4>Discord OAuth</h4>
                                    <p>To enable Discord login, create an application in the Discord Developer Portal:</p>
                                    <div class="code-block">
                                        <pre><code>DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_CALLBACK_URL=https://your-domain.com/auth/discord/callback</code></pre>
                                    </div>
                                    <p><strong>Callback URL:</strong> <code>{BASE_URL}/auth/discord/callback</code></p>
                                    <div class="alert info">
                                        <i class="fas fa-info-circle"></i>
                                        <div>
                                            <strong>Discord Setup:</strong> Go to Discord Developer Portal → Applications → New Application → OAuth2.
                                            Add your BASE_URL + /auth/discord/callback to the Redirects list. Required scopes: identify, email.
                                        </div>
                                    </div>
                                </div>

                                <div class="alert info">
                                    <i class="fas fa-info-circle"></i>
                                    <div>
                                        <strong>OAuth Setup:</strong> For detailed OAuth setup instructions, see our
                                        <a href="/docs/oauth-setup">OAuth Setup Guide</a>.
                                    </div>
                                </div>
                            </div>

                            <!-- Email Configuration -->
                            <div class="config-section">
                                <h3>Email Configuration</h3>
                                <p>Configure email settings for user verification and notifications.</p>

                                <div class="config-item">
                                    <h4>SMTP Settings</h4>
                                    <div class="code-block">
                                        <pre><code>SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password</code></pre>
                                    </div>
                                </div>

                                <div class="config-item">
                                    <h4>Email From Address</h4>
                                    <div class="code-block">
                                        <pre><code>EMAIL_FROM=<EMAIL></code></pre>
                                    </div>
                                </div>

                                <div class="alert warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <div>
                                        <strong>Gmail Users:</strong> Use an App Password instead of your regular password.
                                        Enable 2FA and generate an App Password in your Google Account settings.
                                    </div>
                                </div>
                            </div>

                            <!-- File Upload Configuration -->
                            <div class="config-section">
                                <h3>File Upload Configuration</h3>

                                <div class="config-item">
                                    <h4>MAX_FILE_SIZE</h4>
                                    <p>Maximum file size for mod uploads (in bytes). <strong>Note:</strong> File size limits are now managed through the admin panel and can be set to any value without restrictions.</p>
                                    <div class="code-block">
                                        <pre><code># 100MB limit (optional - admin panel overrides this)
MAX_FILE_SIZE=*********</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> ********* (100MB) - Can be changed in Admin Panel → Settings</p>
                                </div>

                                <div class="config-item">
                                    <h4>UPLOAD_PATH</h4>
                                    <p>Directory for storing uploaded files.</p>
                                    <div class="code-block">
                                        <pre><code>UPLOAD_PATH=./uploads</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> ./uploads</p>
                                </div>
                            </div>

                            <!-- Security Configuration -->
                            <div class="config-section">
                                <h3>Security Configuration</h3>

                                <div class="config-item">
                                    <h4>JWT_SECRET</h4>
                                    <p>Secret key for signing JWT tokens used in API authentication.</p>
                                    <div class="code-block">
                                        <pre><code>JWT_SECRET=your-jwt-secret-key</code></pre>
                                    </div>
                                    <p><strong>Required for API:</strong> Yes</p>
                                </div>

                                <div class="config-item">
                                    <h4>RATE_LIMIT_WINDOW</h4>
                                    <p>Rate limiting window in milliseconds.</p>
                                    <div class="code-block">
                                        <pre><code># 15 minutes
RATE_LIMIT_WINDOW=900000</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> 900000 (15 minutes)</p>
                                </div>

                                <div class="config-item">
                                    <h4>RATE_LIMIT_MAX</h4>
                                    <p>Maximum requests per window.</p>
                                    <div class="code-block">
                                        <pre><code>RATE_LIMIT_MAX=100</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> 100</p>
                                </div>
                            </div>

                            <!-- SSL/HTTPS Configuration -->
                            <div class="config-section">
                                <h3>SSL/HTTPS Configuration</h3>

                                <div class="config-item">
                                    <h4>ENABLE_HTTPS</h4>
                                    <p>Enable HTTPS server with SSL certificates.</p>
                                    <div class="code-block">
                                        <pre><code>ENABLE_HTTPS=true</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> true</p>
                                </div>

                                <div class="config-item">
                                    <h4>SSL Certificate Paths</h4>
                                    <p>Paths to your SSL certificate files when HTTPS is enabled.</p>
                                    <div class="code-block">
                                        <pre><code>SSL_KEY_PATH=./csr/cert.key
SSL_CERT_PATH=./csr/cert.crt</code></pre>
                                    </div>
                                    <p><strong>Note:</strong> Self-signed certificates are included for development. Use proper certificates in production.</p>
                                </div>

                                <div class="config-item">
                                    <h4>TRUST_PROXY_HOPS</h4>
                                    <p>Number of proxy hops to trust (for Cloudflare and reverse proxies).</p>
                                    <div class="code-block">
                                        <pre><code># For Cloudflare
TRUST_PROXY_HOPS=1

# For multiple proxies
TRUST_PROXY_HOPS=2</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> 0 (no proxy trust)</p>
                                </div>
                            </div>

                            <!-- Development Configuration -->
                            <div class="config-section">
                                <h3>Development Configuration</h3>

                                <div class="config-item">
                                    <h4>NODE_ENV</h4>
                                    <p>Environment mode. Set to 'production' for production deployments.</p>
                                    <div class="code-block">
                                        <pre><code># Development
NODE_ENV=development

# Production
NODE_ENV=production</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> development</p>
                                </div>

                                <div class="config-item">
                                    <h4>DEBUG</h4>
                                    <p>Enable debug logging.</p>
                                    <div class="code-block">
                                        <pre><code>DEBUG=true</code></pre>
                                    </div>
                                    <p><strong>Default:</strong> false</p>
                                </div>
                            </div>

                            <!-- Complete Example -->
                            <div class="config-section">
                                <h3>Complete Example</h3>
                                <p>Here's a complete example of a production <code>.env</code> file:</p>

                                <div class="code-block">
                                    <div class="code-header">
                                        <span>Complete .env example</span>
                                        <button class="copy-btn" data-copy="# Basic Configuration
PORT=3000
SESSION_SECRET=your-very-long-random-secret-key-here
BASE_URL=https://your-domain.com
NODE_ENV=production

# HTTPS Configuration
ENABLE_HTTPS=true
SSL_KEY_PATH=./csr/cert.key
SSL_CERT_PATH=./csr/cert.crt

# Database
DATABASE_PATH=./database/sayonika.db
SESSIONS_DATABASE_PATH=./database/sessions.db

# OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=https://your-domain.com/auth/github/callback

DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_CALLBACK_URL=https://your-domain.com/auth/discord/callback

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# File Uploads
MAX_FILE_SIZE=*********
UPLOAD_PATH=./uploads

# Security
JWT_SECRET=your-jwt-secret-key
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# CORS Configuration
ALLOWED_ORIGINS=https://your-domain.com

# Proxy Configuration (for Cloudflare)
TRUST_PROXY_HOPS=1

# External Services (optional)
DISCORD_WEBHOOK_URL=">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <pre><code># Basic Configuration
PORT=3000
SESSION_SECRET=your-very-long-random-secret-key-here
BASE_URL=https://your-domain.com
NODE_ENV=production

# HTTPS Configuration
ENABLE_HTTPS=true
SSL_KEY_PATH=./csr/cert.key
SSL_CERT_PATH=./csr/cert.crt

# Database
DATABASE_PATH=./database/sayonika.db
SESSIONS_DATABASE_PATH=./database/sessions.db

# OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=https://your-domain.com/auth/github/callback

DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_CALLBACK_URL=https://your-domain.com/auth/discord/callback

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# File Uploads
MAX_FILE_SIZE=*********
UPLOAD_PATH=./uploads

# Security
JWT_SECRET=your-jwt-secret-key
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# CORS Configuration
ALLOWED_ORIGINS=https://your-domain.com

# Proxy Configuration (for Cloudflare)
TRUST_PROXY_HOPS=1

# External Services (optional)
DISCORD_WEBHOOK_URL=</code></pre>
                                </div>
                            </div>

                            <!-- Next Steps -->
                            <div class="next-steps">
                                <h3>Next Steps</h3>
                                <div class="next-steps-grid">
                                    <div class="next-step-card">
                                        <div class="step-icon">
                                            <i class="fas fa-play"></i>
                                        </div>
                                        <h4>First Run</h4>
                                        <p>Complete the initial setup and create your admin account.</p>
                                        <a href="/docs/installation#first-run" class="btn btn-outline">First Run Guide</a>
                                    </div>

                                    <div class="next-step-card">
                                        <div class="step-icon">
                                            <i class="fas fa-server"></i>
                                        </div>
                                        <h4>Deployment</h4>
                                        <p>Deploy your Sayonika instance to production.</p>
                                        <a href="/docs/deployment" class="btn btn-outline">Deployment Guide</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../partials/footer') %>
