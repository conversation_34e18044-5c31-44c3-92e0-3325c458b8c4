<%- include('../partials/header', { title: title }) %>
<link rel="stylesheet" href="/css/docs.css">

<!-- Mobile Menu Toggle -->
<button class="docs-mobile-toggle" id="docsMenuToggle">
    <i class="fas fa-bars"></i>
    <span>Menu</span>
</button>

<!-- Documentation Page -->
<div class="docs-page">
    <!-- Main Content -->
    <div class="docs-main">
        <div class="container">
            <div class="docs-layout">
                <!-- Sidebar Navigation -->
                <%- include('../partials/docs-sidebar') %>

                <!-- Content Area -->
                <main class="docs-content" id="docsContent">
                    <!-- Installation Section -->
                    <section class="docs-section" id="installation">
                        <h2>Installation Guide</h2>
                        <div class="section-content">
                            <p class="lead">
                                Get Sayonika up and running on your server with this comprehensive installation guide.
                                This guide covers everything from initial setup to your first admin account.
                            </p>

                            <!-- Prerequisites -->
                            <div class="prerequisites-section">
                                <h3><i class="fas fa-list-check"></i> Prerequisites</h3>
                                <div class="requirements-grid">
                                    <div class="requirement-item">
                                        <div class="req-icon">
                                            <i class="fab fa-node-js"></i>
                                        </div>
                                        <div class="req-content">
                                            <h4>Node.js 16+</h4>
                                            <p>JavaScript runtime environment</p>
                                            <a href="https://nodejs.org" target="_blank" class="req-link">Download</a>
                                        </div>
                                    </div>
                                    <div class="requirement-item">
                                        <div class="req-icon">
                                            <i class="fab fa-git-alt"></i>
                                        </div>
                                        <div class="req-content">
                                            <h4>Git</h4>
                                            <p>Version control system</p>
                                            <a href="https://git-scm.com" target="_blank" class="req-link">Download</a>
                                        </div>
                                    </div>
                                    <div class="requirement-item">
                                        <div class="req-icon">
                                            <i class="fas fa-server"></i>
                                        </div>
                                        <div class="req-content">
                                            <h4>512MB+ RAM</h4>
                                            <p>Minimum system memory</p>
                                        </div>
                                    </div>
                                    <div class="requirement-item">
                                        <div class="req-icon">
                                            <i class="fas fa-hdd"></i>
                                        </div>
                                        <div class="req-content">
                                            <h4>1GB+ Storage</h4>
                                            <p>Free disk space</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Installation -->
                            <div class="installation-section">
                                <h3><i class="fas fa-rocket"></i> Quick Installation</h3>
                                <p>The fastest way to get Sayonika running. Perfect for development and testing.</p>

                                <div class="quick-steps">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>Clone Repository</h4>
                                            <div class="code-block">
                                                <div class="code-header">
                                                    <span>Clone and navigate</span>
                                                    <button class="copy-btn" data-copy="git clone https://github.com/Dynamicaaa/Sayonika.git
cd Sayonika">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                <pre><code>git clone https://github.com/Dynamicaaa/Sayonika.git
cd Sayonika</code></pre>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>Install Dependencies</h4>
                                            <div class="code-block">
                                                <div class="code-header">
                                                    <span>Install packages</span>
                                                    <button class="copy-btn" data-copy="npm install">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                <pre><code>npm install</code></pre>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>Configure Environment</h4>
                                            <div class="code-block">
                                                <div class="code-header">
                                                    <span>Create configuration file</span>
                                                    <button class="copy-btn" data-copy="cp .env.example .env">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                <pre><code>cp .env.example .env</code></pre>
                                            </div>
                                            <p>Edit the <code>.env</code> file with your basic configuration:</p>
                                            <div class="code-block">
                                                <div class="code-header">
                                                    <span>Minimum .env configuration</span>
                                                    <button class="copy-btn" data-copy="PORT=3000
SESSION_SECRET=your-very-long-random-secret-key-here
BASE_URL=http://localhost:3000
JWT_SECRET=your-jwt-secret-key-here">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                <pre><code>PORT=3000
SESSION_SECRET=your-very-long-random-secret-key-here
BASE_URL=http://localhost:3000
JWT_SECRET=your-jwt-secret-key-here</code></pre>
                                            </div>
                                            <div class="alert warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                <div>
                                                    <strong>Security:</strong> Make sure to use strong, unique values for the secret keys in production!
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">4</div>
                                        <div class="step-content">
                                            <h4>Build and Start</h4>
                                            <div class="code-block">
                                                <div class="code-header">
                                                    <span>Build CSS and start server</span>
                                                    <button class="copy-btn" data-copy="npm run build-css-prod
npm run dev">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                <pre><code>npm run build-css-prod
npm run dev</code></pre>
                                            </div>
                                            <div class="alert success">
                                                <i class="fas fa-check-circle"></i>
                                                <div>
                                                    <strong>Success!</strong> Sayonika is now running at <code>http://localhost:3000</code>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- First Run Section -->
                            <div class="first-run-section" id="first-run">
                                <h3><i class="fas fa-user-crown"></i> First Run</h3>
                                <p>After installation, follow these steps to set up your admin account and configure Sayonika.</p>

                                <div class="first-run-steps">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>Access Your Instance</h4>
                                            <p>Open your web browser and navigate to your Sayonika instance:</p>
                                            <div class="code-block">
                                                <pre><code>http://localhost:3000</code></pre>
                                            </div>
                                            <p>You should see the Sayonika homepage with a registration option.</p>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>Create Owner Account</h4>
                                            <p>The first user to register automatically becomes the "Owner" with full administrative privileges.</p>

                                            <div class="registration-options">
                                                <h5>Registration Methods:</h5>
                                                <ul>
                                                    <li><strong>Email Registration:</strong> Click "Register" and create an account with email and password</li>
                                                    <li><strong>GitHub OAuth:</strong> Use GitHub authentication if you've configured OAuth</li>
                                                    <li><strong>Discord OAuth:</strong> Use Discord authentication if you've configured OAuth</li>
                                                </ul>
                                                <p><strong>Note:</strong> OAuth providers must be configured in your <code>.env</code> file. See the <a href="/docs/configuration">Configuration Guide</a> for setup instructions.</p>
                                            </div>

                                            <div class="alert info">
                                                <i class="fas fa-crown"></i>
                                                <div>
                                                    <strong>Owner Privileges:</strong> The first user gets special "Owner" status with elevated privileges over regular admins.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>Access Admin Panel</h4>
                                            <p>Once logged in as the owner, you can access the admin panel:</p>
                                            <ul>
                                                <li>Click on your profile dropdown in the navigation</li>
                                                <li>Select "Admin Panel" from the menu</li>
                                                <li>Or navigate directly to <code>/admin</code></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="step">
                                        <div class="step-number">4</div>
                                        <div class="step-content">
                                            <h4>Configure Settings</h4>
                                            <p>In the admin panel, you can:</p>
                                            <ul>
                                                <li>Review and approve mod submissions with detailed review reasons</li>
                                                <li>Manage users, permissions, and OAuth account linking</li>
                                                <li>Configure maintenance mode with custom messages</li>
                                                <li>Adjust file upload limits and site settings</li>
                                                <li>View platform statistics and user analytics</li>
                                                <li>Manage support tickets and user feedback</li>
                                                <li>Monitor achievement system and user progression</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Production Setup -->
                            <div class="production-section">
                                <h3><i class="fas fa-server"></i> Production Setup</h3>
                                <p>Additional steps for production deployment:</p>

                                <div class="production-checklist">
                                    <div class="checklist-item">
                                        <i class="fas fa-check-square"></i>
                                        <div>
                                            <h4>Environment Variables</h4>
                                            <p>Set <code>NODE_ENV=production</code> and configure all required environment variables including:</p>
                                            <ul>
                                                <li><code>BASE_URL</code> - Your production domain</li>
                                                <li><code>ENABLE_HTTPS=true</code> - Enable HTTPS server</li>
                                                <li><code>TRUST_PROXY_HOPS=1</code> - For Cloudflare/reverse proxy setups</li>
                                                <li>OAuth credentials for GitHub and Discord (optional)</li>
                                            </ul>
                                            <p>See the <a href="/docs/configuration">Configuration</a> page for complete details.</p>
                                        </div>
                                    </div>

                                    <div class="checklist-item">
                                        <i class="fas fa-check-square"></i>
                                        <div>
                                            <h4>Process Manager</h4>
                                            <p>Use PM2 or similar to manage the Node.js process:</p>
                                            <div class="code-block">
                                                <pre><code>npm install -g pm2
pm2 start server.js --name sayonika
pm2 startup
pm2 save</code></pre>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="checklist-item">
                                        <i class="fas fa-check-square"></i>
                                        <div>
                                            <h4>Reverse Proxy</h4>
                                            <p>Configure Nginx or Apache as a reverse proxy for better performance and SSL termination.</p>
                                        </div>
                                    </div>

                                    <div class="checklist-item">
                                        <i class="fas fa-check-square"></i>
                                        <div>
                                            <h4>SSL Certificate</h4>
                                            <p>Sayonika includes built-in HTTPS support. Configure SSL certificates by setting:</p>
                                            <ul>
                                                <li><code>SSL_KEY_PATH</code> - Path to your private key file</li>
                                                <li><code>SSL_CERT_PATH</code> - Path to your certificate file</li>
                                            </ul>
                                            <p>Consider using Let's Encrypt for free certificates, or use the included self-signed certificates for development.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Troubleshooting -->
                            <div class="troubleshooting-section">
                                <h3><i class="fas fa-tools"></i> Common Issues</h3>

                                <div class="faq-item">
                                    <h4>Port already in use</h4>
                                    <p>If port 3000 is already in use, change the <code>PORT</code> value in your <code>.env</code> file to a different port number.</p>
                                </div>

                                <div class="faq-item">
                                    <h4>Database errors</h4>
                                    <p>The database is automatically created when you first run the server. Make sure you have write permissions in the project directory.</p>
                                </div>

                                <div class="faq-item">
                                    <h4>CSS not loading</h4>
                                    <p>Make sure you ran <code>npm run build-css-prod</code> before starting the server.</p>
                                </div>

                                <div class="faq-item">
                                    <h4>Module not found errors</h4>
                                    <p>Run <code>npm install</code> again to ensure all dependencies are properly installed.</p>
                                </div>
                            </div>

                            <!-- Next Steps -->
                            <div class="next-steps-section">
                                <h3><i class="fas fa-arrow-right"></i> What's Next?</h3>
                                <div class="next-steps-grid">
                                    <div class="next-step-card">
                                        <div class="step-icon">
                                            <i class="fas fa-cog"></i>
                                        </div>
                                        <h4>Advanced Configuration</h4>
                                        <p>Set up OAuth providers, email settings, and production configurations.</p>
                                        <a href="/docs/configuration" class="btn btn-outline">Configure Now</a>
                                    </div>

                                    <div class="next-step-card">
                                        <div class="step-icon">
                                            <i class="fas fa-code"></i>
                                        </div>
                                        <h4>API Reference</h4>
                                        <p>Explore the REST API endpoints and start building integrations.</p>
                                        <a href="/docs/api" class="btn btn-outline">API Docs</a>
                                    </div>

                                    <div class="next-step-card">
                                        <div class="step-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </div>
                                        <h4>Need Help?</h4>
                                        <p>Check our troubleshooting guide or get support from the community.</p>
                                        <a href="https://github.com/Dynamicaaa/Sayonika/issues" target="_blank" class="btn btn-outline">Get Support</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar Overlay for Mobile -->
<div class="docs-sidebar-overlay" id="sidebarOverlay"></div>

<script src="/js/docs.js?v=<%= Date.now() %>"></script>
<%- include('../partials/footer') %>