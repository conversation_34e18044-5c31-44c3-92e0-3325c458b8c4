<%- include('partials/header', { title: title }) %>

<section class="error-section">
    <div class="container">
        <div class="error-content">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1 class="error-title"><%= title %></h1>
            <p class="error-message"><%= message %></p>
            
            <div class="error-actions">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i> Go Home
                </a>
                <button onclick="history.back()" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i> Go Back
                </button>
            </div>
            
            <% if (locals.error && process.env.NODE_ENV === 'development') { %>
                <div class="error-details">
                    <h3>Error Details (Development Mode)</h3>
                    <pre><%= error.stack %></pre>
                </div>
            <% } %>
        </div>
    </div>
</section>

<%- include('partials/footer') %>
