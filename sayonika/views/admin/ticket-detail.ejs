<%- include('../partials/header', { title: title, styles: ['/css/admin.css'] }) %>

<div class="admin-page">
    <div class="container">
        <!-- Admin Header -->
        <div class="admin-header">
            <div class="breadcrumb">
                <a href="/admin" class="breadcrumb-link">
                    <i class="fas fa-shield-alt"></i> Admin Dashboard
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Support Ticket #<%= ticket.id %></span>
            </div>
            <h1>Support Ticket #<%= ticket.id %></h1>
        </div>

        <!-- Ticket Content -->
        <div class="ticket-detail-container">
            <!-- Ticket Header -->
            <div class="ticket-header">
                <div class="ticket-info">
                    <h2 class="ticket-subject"><%= ticket.subject %></h2>
                    <div class="ticket-meta">
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span class="meta-label">From:</span>
                            <span class="meta-value">
                                <%= ticket.name %>
                                <% if (ticket.username) { %>
                                    (<a href="/user/<%= ticket.username %>" target="_blank">@<%= ticket.username %></a>)
                                <% } %>
                            </span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-envelope"></i>
                            <span class="meta-label">Email:</span>
                            <span class="meta-value"><%= ticket.email %></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span class="meta-label">Created:</span>
                            <span class="meta-value"><%= new Date(ticket.created_at).toLocaleString() %></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span class="meta-label">Updated:</span>
                            <span class="meta-value"><%= new Date(ticket.updated_at).toLocaleString() %></span>
                        </div>
                    </div>
                </div>

                <!-- Ticket Status & Priority -->
                <div class="ticket-status-section">
                    <div class="status-priority-row">
                        <div class="status-group">
                            <label class="form-label">Status:</label>
                            <select class="form-control status-select" id="ticketStatus" data-ticket-id="<%= ticket.id %>">
                                <option value="open" <%= ticket.status === 'open' ? 'selected' : '' %>>Open</option>
                                <option value="in_progress" <%= ticket.status === 'in_progress' ? 'selected' : '' %>>In Progress</option>
                                <option value="resolved" <%= ticket.status === 'resolved' ? 'selected' : '' %>>Resolved</option>
                                <option value="closed" <%= ticket.status === 'closed' ? 'selected' : '' %>>Closed</option>
                            </select>
                        </div>
                        <div class="priority-group">
                            <label class="form-label">Priority:</label>
                            <select class="form-control priority-select" id="ticketPriority" data-ticket-id="<%= ticket.id %>">
                                <option value="low" <%= ticket.priority === 'low' ? 'selected' : '' %>>Low</option>
                                <option value="medium" <%= ticket.priority === 'medium' ? 'selected' : '' %>>Medium</option>
                                <option value="high" <%= ticket.priority === 'high' ? 'selected' : '' %>>High</option>
                                <option value="urgent" <%= ticket.priority === 'urgent' ? 'selected' : '' %>>Urgent</option>
                            </select>
                        </div>
                    </div>

                    <!-- Status and Priority Badges -->
                    <div class="badges-row">
                        <span class="status-badge status-<%= ticket.status %>">
                            <i class="fas fa-<%= ticket.status === 'open' ? 'exclamation-circle' : ticket.status === 'in_progress' ? 'spinner' : ticket.status === 'resolved' ? 'check-circle' : 'times-circle' %>"></i>
                            <%= ticket.status.replace('_', ' ').toUpperCase() %>
                        </span>
                        <span class="priority-badge priority-<%= ticket.priority %>">
                            <i class="fas fa-<%= ticket.priority === 'urgent' ? 'exclamation-triangle' : ticket.priority === 'high' ? 'arrow-up' : ticket.priority === 'medium' ? 'minus' : 'arrow-down' %>"></i>
                            <%= ticket.priority.toUpperCase() %> PRIORITY
                        </span>
                    </div>
                </div>
            </div>

            <!-- Ticket Message -->
            <div class="ticket-message-section">
                <h3>Message</h3>
                <div class="ticket-message">
                    <%= ticket.message %>
                </div>
            </div>

            <!-- Admin Actions -->
            <div class="admin-actions-section">
                <h3>Admin Actions</h3>
                <div class="action-buttons">
                    <button class="btn btn-success" id="resolveTicketBtn" data-ticket-id="<%= ticket.id %>"
                            <%= ticket.status === 'resolved' || ticket.status === 'closed' ? 'disabled' : '' %>>
                        <i class="fas fa-check"></i> Mark as Resolved
                    </button>
                    <button class="btn btn-primary" id="inProgressTicketBtn" data-ticket-id="<%= ticket.id %>"
                            <%= ticket.status === 'in_progress' ? 'disabled' : '' %>>
                        <i class="fas fa-play"></i> Mark as In Progress
                    </button>
                    <button class="btn btn-secondary" id="closeTicketBtn" data-ticket-id="<%= ticket.id %>"
                            <%= ticket.status === 'closed' ? 'disabled' : '' %>>
                        <i class="fas fa-times"></i> Close Ticket
                    </button>
                    <button class="btn btn-danger" id="deleteTicketBtn" data-ticket-id="<%= ticket.id %>">
                        <i class="fas fa-trash"></i> Delete Ticket
                    </button>
                </div>
            </div>

            <!-- Back to Dashboard -->
            <div class="navigation-section">
                <a href="/admin" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" class="message-container" style="display: none;">
    <div class="alert" id="messageAlert">
        <span id="messageText"></span>
        <button type="button" class="close-btn" onclick="hideMessage()">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
// Ticket management functions
function updateTicketStatus(ticketId, status) {
    fetch(`/api/admin/support/tickets/${ticketId}/status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage(data.error, 'error');
        } else {
            showMessage('Ticket status updated successfully', 'success');
            // Update the status badge
            updateStatusBadge(status);
            // Update button states
            updateButtonStates(status);
        }
    })
    .catch(error => {
        console.error('Error updating ticket status:', error);
        showMessage('Failed to update ticket status', 'error');
    });
}

function updateStatusBadge(status) {
    const badge = document.querySelector('.status-badge');
    badge.className = `status-badge status-${status}`;
    badge.innerHTML = `<i class="fas fa-${status === 'open' ? 'exclamation-circle' : status === 'in_progress' ? 'spinner' : status === 'resolved' ? 'check-circle' : 'times-circle'}"></i> ${status.replace('_', ' ').toUpperCase()}`;
}

function updateTicketPriority(ticketId, priority) {
    fetch(`/api/admin/support/tickets/${ticketId}/priority`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ priority })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage(data.error, 'error');
        } else {
            showMessage('Ticket priority updated successfully', 'success');
            // Update the priority badge
            updatePriorityBadge(priority);
        }
    })
    .catch(error => {
        console.error('Error updating ticket priority:', error);
        showMessage('Failed to update ticket priority', 'error');
    });
}

function updateButtonStates(status) {
    const resolveBtn = document.getElementById('resolveTicketBtn');
    const inProgressBtn = document.getElementById('inProgressTicketBtn');
    const closeBtn = document.getElementById('closeTicketBtn');

    resolveBtn.disabled = (status === 'resolved' || status === 'closed');
    inProgressBtn.disabled = (status === 'in_progress');
    closeBtn.disabled = (status === 'closed');
}

function updatePriorityBadge(priority) {
    const badge = document.querySelector('.priority-badge');
    badge.className = `priority-badge priority-${priority}`;
    badge.innerHTML = `<i class="fas fa-${priority === 'urgent' ? 'exclamation-triangle' : priority === 'high' ? 'arrow-up' : priority === 'medium' ? 'minus' : 'arrow-down'}"></i> ${priority.toUpperCase()} PRIORITY`;
}

function deleteTicket(ticketId) {
    if (!confirm('Are you sure you want to delete this ticket? This action cannot be undone.')) {
        return;
    }

    fetch(`/api/admin/support/tickets/${ticketId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showMessage(data.error, 'error');
        } else {
            showMessage('Ticket deleted successfully', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        }
    })
    .catch(error => {
        console.error('Error deleting ticket:', error);
        showMessage('Failed to delete ticket', 'error');
    });
}

function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alert = document.getElementById('messageAlert');
    const text = document.getElementById('messageText');

    text.textContent = message;
    alert.className = `alert alert-${type}`;
    container.style.display = 'block';

    setTimeout(hideMessage, 5000);
}

function hideMessage() {
    document.getElementById('messageContainer').style.display = 'none';
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    const ticketId = <%= ticket.id %>;

    // Status change handler
    document.getElementById('ticketStatus').addEventListener('change', function() {
        updateTicketStatus(ticketId, this.value);
    });

    // Priority change handler
    document.getElementById('ticketPriority').addEventListener('change', function() {
        updateTicketPriority(ticketId, this.value);
    });

    // Action button handlers
    document.getElementById('resolveTicketBtn').addEventListener('click', function() {
        updateTicketStatus(ticketId, 'resolved');
    });

    document.getElementById('inProgressTicketBtn').addEventListener('click', function() {
        updateTicketStatus(ticketId, 'in_progress');
    });

    document.getElementById('closeTicketBtn').addEventListener('click', function() {
        updateTicketStatus(ticketId, 'closed');
    });

    document.getElementById('deleteTicketBtn').addEventListener('click', function() {
        deleteTicket(ticketId);
    });
});
</script>

<%- include('../partials/footer') %>
