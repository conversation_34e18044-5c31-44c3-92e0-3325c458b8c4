<%- include('../partials/header', { title: title }) %>

<div class="auth-page">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">
                    <i class="fas fa-heart"></i>
                </div>
                <h1>Welcome Back</h1>
                <p>Sign in to your Sayonika account</p>
            </div>

            <% if (message) { %>
                <div class="alert alert-<%= messageType === 'error' ? 'error' : messageType %>">
                    <i class="fas fa-<%= messageType === 'error' ? 'exclamation-circle' : messageType === 'warning' ? 'exclamation-triangle' : 'info-circle' %>"></i>
                    <%= message %>
                </div>
            <% } %>

            <form class="auth-form" id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">Username or Email</label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        class="form-control"
                        required
                        autocomplete="username"
                        placeholder="Enter your username or email"
                    >
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-control"
                        required
                        autocomplete="current-password"
                        placeholder="Enter your password"
                    >
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" id="remember" name="remember" class="form-check-input">
                        <label for="remember" class="form-check-label">Remember me</label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <span class="btn-text">Sign In</span>
                </button>
            </form>

            <div class="auth-options">
                <a href="/forgot-password" class="forgot-password">Forgot your password?</a>
            </div>

            <div class="auth-divider">
                <span>or</span>
            </div>

            <div class="social-auth">
                <% if (process.env.GITHUB_CLIENT_ID) { %>
                    <a href="/auth/github" class="social-btn github" id="githubOAuth">
                        <i class="fab fa-github"></i>
                        Continue with GitHub
                    </a>
                <% } %>
                <% if (process.env.DISCORD_CLIENT_ID) { %>
                    <a href="/auth/discord" class="social-btn discord" id="discordOAuth">
                        <i class="fab fa-discord"></i>
                        Continue with Discord
                    </a>
                <% } %>
            </div>

            <div class="auth-footer">
                <p>
                    Don't have an account?
                    <a href="/register">Create one here</a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    animation: alertSlide 0.3s ease-out;
}

.alert i {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.alert-error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

/* Dark theme alert styles */
body.dark-theme .alert-error {
    background: rgba(220, 53, 69, 0.15);
    color: #ff6b6b;
    border-color: rgba(220, 53, 69, 0.3);
}

body.dark-theme .alert-warning {
    background: rgba(255, 193, 7, 0.15);
    color: #ffd93d;
    border-color: rgba(255, 193, 7, 0.3);
}

body.dark-theme .alert-info {
    background: rgba(23, 162, 184, 0.15);
    color: #4ecdc4;
    border-color: rgba(23, 162, 184, 0.3);
}

body.dark-theme .alert-success {
    background: rgba(40, 167, 69, 0.15);
    color: #51cf66;
    border-color: rgba(40, 167, 69, 0.3);
}

@keyframes alertSlide {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<%- include('../partials/footer', { scripts: ['/js/pages/login.js'] }) %>
