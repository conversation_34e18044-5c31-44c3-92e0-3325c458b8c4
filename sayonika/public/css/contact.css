/* Contact Page Styles */

.support-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
}

.support-option {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.support-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.github-option {
    border-color: #24292e;
}

.github-option:hover {
    border-color: #0366d6;
    background: linear-gradient(135deg, #f6f8fa 0%, #e1e4e8 100%);
}

.ticket-option {
    border-color: #6f42c1;
}

.ticket-option:hover {
    border-color: #5a32a3;
    background: linear-gradient(135deg, #f8f6ff 0%, #e6e0ff 100%);
}

.option-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.option-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

.github-option .option-icon {
    background: linear-gradient(135deg, #24292e 0%, #0366d6 100%);
}

.ticket-option .option-icon {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
}

.option-header h2 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1.5rem;
}

.option-header p {
    color: #666;
    margin: 0;
    font-size: 0.95rem;
}

.option-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.option-benefits li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #555;
    font-size: 0.9rem;
}

.option-benefits li i {
    color: #28a745;
    margin-right: 0.75rem;
    font-size: 0.8rem;
}

.login-note {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    color: #856404;
    font-size: 0.85rem;
    text-align: center;
}

.login-note i {
    margin-right: 0.5rem;
}

/* Ticket Form Styles */
.ticket-form-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

.ticket-form {
    background: white;
    border-radius: 15px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.form-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ticket-form form {
    padding: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #6f42c1;
    box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

/* Message Container */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    max-width: 400px;
}

.message-content {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

.message-content.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.message-content.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: 1rem;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.message-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .support-options {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .ticket-form {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }
    
    .ticket-form form {
        padding: 1.5rem;
    }
    
    .form-header {
        padding: 1rem 1.5rem;
    }
    
    .message-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}
