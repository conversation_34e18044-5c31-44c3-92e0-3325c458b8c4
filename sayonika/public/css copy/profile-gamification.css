/* Profile Gamification Styles */

/* Profile Level Info */
.profile-level {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0.5rem 0;
    flex-wrap: wrap;
}

.user-title {
    background: linear-gradient(135deg, var(--primary-color, #ff6b9d), var(--secondary-color, #4ecdc4));
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.user-level {
    background: var(--card-bg, #f8f9fa);
    border: 1px solid var(--border-color, #dee2e6);
    color: var(--text-primary, #333);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.achievement-points {
    color: var(--warning-color, #ffc107);
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Achievements List */
.achievements-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.achievement-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--card-bg, #f8f9fa);
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.achievement-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.achievement-item.earned {
    border-color: var(--success-color, #28a745);
    background: linear-gradient(135deg, var(--card-bg, #f8f9fa) 0%, rgba(40, 167, 69, 0.05) 100%);
}

.achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: var(--bg-secondary, #e9ecef);
    color: var(--text-muted, #666);
    flex-shrink: 0;
}

.achievement-icon.earned {
    background: linear-gradient(135deg, var(--success-color, #28a745), var(--primary-color, #ff6b9d));
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.achievement-details {
    flex: 1;
    min-width: 0;
}

.achievement-name {
    font-weight: 600;
    color: var(--text-primary, #333);
    margin-bottom: 0.25rem;
}

.achievement-description {
    color: var(--text-muted, #666);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.achievement-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.achievement-points {
    color: var(--warning-color, #ffc107);
    font-size: 0.8rem;
    font-weight: 500;
}

.achievement-date {
    color: var(--text-muted, #666);
    font-size: 0.8rem;
}

/* No Achievements State */
.no-achievements {
    text-align: center;
    padding: 2rem 1rem;
    color: var(--text-muted, #666);
}

.no-achievements .empty-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-achievements p {
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    font-size: 0.875rem;
    margin-bottom: 1.5rem !important;
}

/* Level Progress */
.level-progress {
    padding: 1rem;
    background: var(--card-bg, #f8f9fa);
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 8px;
}

.level-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.current-level {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.level-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-color, #ff6b9d);
}

.level-title {
    font-size: 0.875rem;
    color: var(--text-muted, #666);
}

.level-points {
    font-weight: 600;
    color: var(--warning-color, #ffc107);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary, #e9ecef);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color, #ff6b9d), var(--secondary-color, #4ecdc4));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-muted, #666);
}

/* Section Links */
.section-link {
    font-size: 0.8rem;
    color: var(--primary-color, #ff6b9d);
    text-decoration: none;
    margin-left: auto;
}

.section-link:hover {
    text-decoration: underline;
}

.sidebar-section h3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Dark Theme Support */
body.dark-theme .achievement-item,
body.dark-theme .level-progress {
    background: var(--card-bg, #2d2d2d);
    border-color: var(--border-color, #3d3d3d);
}

body.dark-theme .achievement-item.earned {
    background: linear-gradient(135deg, var(--card-bg, #2d2d2d) 0%, rgba(40, 167, 69, 0.1) 100%);
}

body.dark-theme .user-level {
    background: var(--card-bg, #2d2d2d);
    border-color: var(--border-color, #3d3d3d);
    color: var(--text-primary, #fff);
}

body.dark-theme .achievement-icon {
    background: var(--bg-secondary, #3d3d3d);
    color: var(--text-muted, #ccc);
}

body.dark-theme .achievement-name {
    color: var(--text-primary, #fff);
}

body.dark-theme .achievement-description,
body.dark-theme .achievement-date,
body.dark-theme .level-title,
body.dark-theme .progress-text,
body.dark-theme .no-achievements,
body.dark-theme .empty-subtitle {
    color: var(--text-muted, #ccc);
}

body.dark-theme .progress-bar {
    background: var(--bg-secondary, #3d3d3d);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-level {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .achievement-item {
        padding: 0.75rem;
    }
    
    .achievement-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
    
    .achievement-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .level-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .user-title,
    .user-level {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
    }
    
    .achievement-points {
        font-size: 0.8rem;
    }
    
    .level-progress {
        padding: 0.75rem;
    }
    
    .no-achievements {
        padding: 1.5rem 0.75rem;
    }
    
    .no-achievements .empty-icon {
        font-size: 2rem;
    }
}
