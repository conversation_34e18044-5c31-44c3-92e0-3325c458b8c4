// Utility classes
@use 'sass:map';
@use 'variables' as *;
@use 'mixins' as *;

// Display utilities
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// Responsive display utilities
@each $breakpoint in map.keys($breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    .d-#{$breakpoint}-none { display: none !important; }
    .d-#{$breakpoint}-inline { display: inline !important; }
    .d-#{$breakpoint}-inline-block { display: inline-block !important; }
    .d-#{$breakpoint}-block { display: block !important; }
    .d-#{$breakpoint}-table { display: table !important; }
    .d-#{$breakpoint}-table-row { display: table-row !important; }
    .d-#{$breakpoint}-table-cell { display: table-cell !important; }
    .d-#{$breakpoint}-flex { display: flex !important; }
    .d-#{$breakpoint}-inline-flex { display: inline-flex !important; }
  }
}

// Flexbox utilities
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

// Text utilities
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-wrap { white-space: normal !important; }
.text-nowrap { white-space: nowrap !important; }
.text-truncate { @include text-truncate(); }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.font-weight-light { font-weight: $font-weight-light !important; }
.font-weight-normal { font-weight: $font-weight-normal !important; }
.font-weight-medium { font-weight: $font-weight-medium !important; }
.font-weight-bold { font-weight: $font-weight-bold !important; }

.font-italic { font-style: italic !important; }

// Text colors
.text-primary { color: $primary-pink !important; }
.text-secondary { color: $primary-blue !important; }
.text-success { color: $success !important; }
.text-info { color: $info !important; }
.text-warning { color: $warning !important; }
.text-danger { color: $danger !important; }
.text-light { color: $light-text-muted !important; }
.text-dark { color: $light-text !important; }
.text-muted { color: $light-text-muted !important; }
.text-white { color: #fff !important; }

// Dark theme text colors
body.dark-theme {
  .text-primary { color: $secondary-pink !important; }
  .text-light { color: $dark-text-muted !important; }
  .text-dark { color: $dark-text !important; }
  .text-muted { color: $dark-text-muted !important; }
}

// Background colors
.bg-primary { background-color: $primary-pink !important; }
.bg-secondary { background-color: $primary-blue !important; }
.bg-success { background-color: $success !important; }
.bg-info { background-color: $info !important; }
.bg-warning { background-color: $warning !important; }
.bg-danger { background-color: $danger !important; }
.bg-light { background-color: $light-surface !important; }
.bg-dark { background-color: $dark-surface !important; }
.bg-white { background-color: #fff !important; }
.bg-transparent { background-color: transparent !important; }

// Dark theme background colors
body.dark-theme {
  .bg-light { background-color: $dark-surface !important; }
  .bg-dark { background-color: $light-surface !important; }
}

// Border utilities
.border { border: $border-width solid $border-color !important; }
.border-top { border-top: $border-width solid $border-color !important; }
.border-right { border-right: $border-width solid $border-color !important; }
.border-bottom { border-bottom: $border-width solid $border-color !important; }
.border-left { border-left: $border-width solid $border-color !important; }

.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

.border-primary { border-color: $primary-pink !important; }
.border-secondary { border-color: $primary-blue !important; }
.border-success { border-color: $success !important; }
.border-info { border-color: $info !important; }
.border-warning { border-color: $warning !important; }
.border-danger { border-color: $danger !important; }
.border-light { border-color: $border-color !important; }
.border-dark { border-color: $dark-surface !important; }
.border-white { border-color: #fff !important; }

// Dark theme border colors
body.dark-theme {
  .border { border-color: $dark-surface-light !important; }
  .border-top { border-top-color: $dark-surface-light !important; }
  .border-right { border-right-color: $dark-surface-light !important; }
  .border-bottom { border-bottom-color: $dark-surface-light !important; }
  .border-left { border-left-color: $dark-surface-light !important; }
  .border-primary { border-color: $secondary-pink !important; }
  .border-light { border-color: $dark-surface-light !important; }
  .border-dark { border-color: $light-surface !important; }
}

// Border radius utilities
.rounded { border-radius: $border-radius !important; }
.rounded-sm { border-radius: $border-radius-sm !important; }
.rounded-lg { border-radius: $border-radius-lg !important; }
.rounded-xl { border-radius: $border-radius-xl !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: 50rem !important; }
.rounded-0 { border-radius: 0 !important; }

.rounded-top { @include border-top-radius($border-radius); }
.rounded-right { @include border-right-radius($border-radius); }
.rounded-bottom { @include border-bottom-radius($border-radius); }
.rounded-left { @include border-left-radius($border-radius); }

// Position utilities
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// Positioning
.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: $z-index-fixed;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $z-index-fixed;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
}

// Shadows
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: $box-shadow-sm !important; }
.shadow { box-shadow: $box-shadow !important; }
.shadow-lg { box-shadow: $box-shadow-lg !important; }

// Width and height utilities
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

// Overflow utilities
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

// Visibility utilities
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

// Screen reader utilities
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.sr-only-focusable {
  &:active,
  &:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
  }
}
