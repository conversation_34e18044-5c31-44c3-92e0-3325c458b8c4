// Form component styles
@use '../variables' as *;
@use '../mixins' as *;

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: $font-weight-medium;
  color: $light-text;
}

// Dark theme form label
body.dark-theme .form-label {
  color: $dark-text;
}

.form-control {
  display: block;
  width: 100%;
  padding: $input-padding-y $input-padding-x;
  font-size: $input-font-size;
  font-weight: $font-weight-normal;
  line-height: $input-line-height;
  color: $light-text;
  background-color: $light-bg;
  background-clip: padding-box;
  border: 1px solid $input-border-color;
  border-radius: $input-border-radius;
  @include transition(border-color, box-shadow);

  &:focus {
    color: $light-text;
    background-color: $light-bg;
    border-color: $primary-pink;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba($primary-pink, 0.25);
  }

  &::placeholder {
    color: $light-text-muted;
    opacity: 1;
  }

  &:disabled,
  &[readonly] {
    background-color: $light-surface;
    opacity: 1;
  }
}

// Dark theme form control
body.dark-theme .form-control {
  color: $dark-text;
  background-color: $dark-surface;
  border-color: $dark-surface-light;

  &:focus {
    color: $dark-text;
    background-color: $dark-surface;
    border-color: $secondary-pink;
    box-shadow: 0 0 0 0.2rem rgba($secondary-pink, 0.25);
  }

  &::placeholder {
    color: $dark-text-muted;
  }

  &:disabled,
  &[readonly] {
    background-color: $dark-surface-light;
  }
}

.form-control-sm {
  padding: 0.375rem 0.75rem;
  font-size: $font-size-sm;
  border-radius: $border-radius-sm;
}

.form-control-lg {
  padding: 0.75rem 1rem;
  font-size: $font-size-lg;
  border-radius: $border-radius-lg;
}

// Textarea
textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

// Select
select.form-control {
  &:focus::-ms-value {
    color: $light-text;
    background-color: $light-bg;
  }
}

// Dark theme select
body.dark-theme select.form-control {
  &:focus::-ms-value {
    color: $dark-text;
    background-color: $dark-surface;
  }
}

// File input
.form-file {
  position: relative;
  display: inline-block;
  width: 100%;
}

.form-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin: 0;
  opacity: 0;

  &:focus ~ .form-file-label {
    border-color: $primary-pink;
    box-shadow: 0 0 0 0.2rem rgba($primary-pink, 0.25);
  }
}

.form-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(2.25rem + 2px);
  padding: $input-padding-y $input-padding-x;
  font-weight: $font-weight-normal;
  line-height: $input-line-height;
  color: $light-text-muted;
  background-color: $light-bg;
  border: 1px solid $input-border-color;
  border-radius: $input-border-radius;
  @include transition(border-color, box-shadow);
}

// Dark theme file input
body.dark-theme .form-file-label {
  color: $dark-text-muted;
  background-color: $dark-surface;
  border-color: $dark-surface-light;
}

.form-file-text {
  display: block;
  @include text-truncate();
}

.form-file-button {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 3;
  height: calc(2.25rem);
  padding: $input-padding-y $input-padding-x;
  margin-left: -1px;
  line-height: $input-line-height;
  color: $light-text;
  background-color: $light-surface;
  border: 1px solid $input-border-color;
  border-radius: 0 $input-border-radius $input-border-radius 0;
}

// Dark theme file button
body.dark-theme .form-file-button {
  color: $dark-text;
  background-color: $dark-surface-light;
  border-color: $dark-surface-light;
}

// Checkbox and radio
.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
  margin-bottom: 0.5rem;
}

// Enhanced Checkboxes with Glass/Aero Effects
.form-check-input {
  appearance: none;
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
  width: 20px;
  height: 20px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  min-width: 20px;

  // Custom checkmark
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 2px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover {
    border-color: rgba($primary-pink, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 15px rgba($primary-pink, 0.3);
    transform: translateY(-1px);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-pink, 0.25);
    border-color: rgba($primary-pink, 0.8);
  }

  &:checked {
    background: linear-gradient(135deg, $primary-pink, rgba($primary-pink, 0.8));
    border-color: $primary-pink;
    box-shadow: 0 0 20px rgba($primary-pink, 0.4);

    &::before {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  &:active {
    transform: translateY(0) scale(0.95);
  }
}

// Dark theme enhanced checkbox
body.dark-theme .form-check-input {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);

  &:hover {
    border-color: rgba($secondary-pink, 0.6);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba($secondary-pink, 0.3);
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($secondary-pink, 0.25);
    border-color: rgba($secondary-pink, 0.8);
  }

  &:checked {
    background: linear-gradient(135deg, $secondary-pink, rgba($secondary-pink, 0.8));
    border-color: $secondary-pink;
    box-shadow: 0 0 20px rgba($secondary-pink, 0.4);
  }
}

.form-check-label {
  color: $light-text;
  cursor: pointer;
}

// Dark theme check label
body.dark-theme .form-check-label {
  color: $dark-text;
}

// Enhanced Rating Option Styling
.rating-option {
  position: relative;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba($primary-pink, 0.3);
    box-shadow: 0 4px 20px rgba($primary-pink, 0.1);
    transform: translateY(-2px);
  }

  .form-check-input {
    position: static;
    margin: 0;
    margin-right: 0.75rem;

    &:checked {
      & + .form-check-label .rating-icon {
        color: $primary-pink;
        transform: scale(1.1);
      }
    }
  }

  .form-check-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin: 0;
    cursor: pointer;
  }

  .rating-info {
    flex: 1;

    .rating-title {
      display: block;
      font-weight: 600;
      font-size: 1rem;
      margin-bottom: 0.25rem;
      color: $light-text;
    }

    .rating-desc {
      display: block;
      font-size: 0.875rem;
      color: $light-text-muted;
      line-height: 1.4;
    }
  }

  .rating-icon {
    font-size: 1.25rem;
    color: $light-text-muted;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// Dark theme rating options
body.dark-theme .rating-option {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.05);

  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba($secondary-pink, 0.3);
    box-shadow: 0 4px 20px rgba($secondary-pink, 0.1);
  }

  .form-check-input:checked + .form-check-label .rating-icon {
    color: $secondary-pink;
  }

  .rating-info {
    .rating-title {
      color: $dark-text;
    }

    .rating-desc {
      color: $dark-text-muted;
    }
  }

  .rating-icon {
    color: $dark-text-muted;
  }
}

.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;

  .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: 0.3125rem;
    margin-left: 0;
  }
}

// Form validation
.is-valid {
  border-color: $success;

  &:focus {
    border-color: $success;
    box-shadow: 0 0 0 0.2rem rgba($success, 0.25);
  }
}

.is-invalid {
  border-color: $danger;

  &:focus {
    border-color: $danger;
    box-shadow: 0 0 0 0.2rem rgba($danger, 0.25);
  }
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: $font-size-sm;
  color: $success;
}

.invalid-feedback,
.form-error {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: $font-size-sm;
  color: $danger;
}

// Form text
.form-text {
  display: block;
  margin-top: 0.25rem;
  font-size: $font-size-sm;
  color: $light-text-muted;
}

// Dark theme form text
body.dark-theme .form-text {
  color: $dark-text-muted;
}

// Input groups
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;

  > .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    margin-bottom: 0;
  }
}

.input-group-prepend,
.input-group-append {
  display: flex;

  .btn {
    position: relative;
    z-index: 2;
  }
}

.input-group-prepend {
  margin-right: -1px;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: $input-padding-y $input-padding-x;
  margin-bottom: 0;
  font-size: $input-font-size;
  font-weight: $font-weight-normal;
  line-height: $input-line-height;
  color: $light-text;
  text-align: center;
  white-space: nowrap;
  background-color: $light-surface;
  border: 1px solid $input-border-color;
  border-radius: $input-border-radius;
}

// Dark theme input group text
body.dark-theme .input-group-text {
  color: $dark-text;
  background-color: $dark-surface-light;
  border-color: $dark-surface-light;
}

// Border radius adjustments for input groups
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .form-control:not(:last-child) {
  @include border-right-radius(0);
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .form-control:not(:first-child) {
  @include border-left-radius(0);
}
