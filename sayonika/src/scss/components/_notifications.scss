// Notifications styles
@use '../variables' as *;
@use '../mixins' as *;
@use 'sass:color';

// Notifications menu in header
.notifications-menu {
  position: relative;
  display: flex;
  align-items: center;
}

.notifications-toggle {
  position: relative;
  background: none;
  border: none;
  color: $light-text-muted;
  font-size: 1.25rem;
  padding: 0.5rem;
  border-radius: 50%;
  cursor: pointer;
  @include transition();

  &:hover {
    background-color: rgba($primary-pink, 0.1);
    color: $primary-pink;
  }

  &.active {
    background-color: rgba($primary-pink, 0.1);
    color: $primary-pink;
  }
}

// Dark theme notifications toggle
body.dark-theme .notifications-toggle {
  color: $dark-text-muted;

  &:hover, &.active {
    background-color: rgba($secondary-pink, 0.1);
    color: $secondary-pink;
  }
}

// Notification badge
.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background-color: $danger;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 1.25rem;
  text-align: center;
  line-height: 1;
  @include transition();

  &.pulse {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

// Notifications dropdown
.notifications-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 380px;
  max-width: 90vw;
  background-color: $light-surface;
  border: 1px solid $border-color;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-lg;
  z-index: $z-index-dropdown;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  @include transition();

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

// Dark theme dropdown
body.dark-theme .notifications-dropdown {
  background-color: $dark-surface;
  border-color: $dark-surface-light;
}

// Notifications header
.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid $border-color;

  h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: $light-text;
  }

  .mark-all-read-btn {
    background: none;
    border: none;
    color: $light-text-muted;
    font-size: 0.875rem;
    padding: 0.25rem;
    border-radius: $border-radius;
    cursor: pointer;
    @include transition();

    &:hover {
      background-color: rgba($primary-pink, 0.1);
      color: $primary-pink;
    }
  }
}

// Dark theme header
body.dark-theme .notifications-header {
  border-bottom-color: $dark-surface-light;

  h4 {
    color: $dark-text;
  }

  .mark-all-read-btn {
    color: $dark-text-muted;

    &:hover {
      background-color: rgba($secondary-pink, 0.1);
      color: $secondary-pink;
    }
  }
}

// Notifications list
.notifications-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($light-text-muted, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba($light-text-muted, 0.5);
  }
}

// Dark theme scrollbar
body.dark-theme .notifications-list {
  &::-webkit-scrollbar-thumb {
    background: rgba($dark-text-muted, 0.3);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba($dark-text-muted, 0.5);
  }
}

// Loading state
.loading-notifications {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: $light-text-muted;
  font-size: 0.875rem;

  i {
    font-size: 1rem;
  }
}

// Dark theme loading
body.dark-theme .loading-notifications {
  color: $dark-text-muted;
}

// Empty state
.empty-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: $light-text-muted;

  i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
  }

  h5 {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.75rem;
    opacity: 0.8;
  }
}

// Dark theme empty state
body.dark-theme .empty-notifications {
  color: $dark-text-muted;
}

// Notification item
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid $border-color;
  cursor: pointer;
  @include transition();

  &:hover {
    background-color: rgba($primary-pink, 0.05);
  }

  &:last-child {
    border-bottom: none;
  }

  &.unread {
    background-color: rgba($primary-pink, 0.08);
    border-left: 3px solid $primary-pink;
    padding-left: calc(1rem - 3px);

    .notification-title {
      font-weight: 600;
    }
  }
}

// Dark theme notification item
body.dark-theme .notification-item {
  border-bottom-color: $dark-surface-light;

  &:hover {
    background-color: rgba($secondary-pink, 0.05);
  }

  &.unread {
    background-color: rgba($secondary-pink, 0.08);
    border-left-color: $secondary-pink;
  }
}

// Notification icon
.notification-icon {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  margin-top: 0.125rem;

  &.achievement {
    background-color: rgba($warning, 0.1);
    color: $warning;
  }

  &.mod-approved {
    background-color: rgba($success, 0.1);
    color: $success;
  }

  &.mod-rejected {
    background-color: rgba($danger, 0.1);
    color: $danger;
  }

  &.general {
    background-color: rgba($info, 0.1);
    color: $info;
  }
}

// Notification content
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: $light-text;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.notification-message {
  font-size: 0.75rem;
  color: $light-text-muted;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 0.6875rem;
  color: $light-text-muted;
  opacity: 0.8;
}

// Dark theme notification content
body.dark-theme {
  .notification-title {
    color: $dark-text;
  }

  .notification-message,
  .notification-time {
    color: $dark-text-muted;
  }
}

// Notifications footer
.notifications-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid $border-color;
  text-align: center;

  .view-all-btn {
    color: $primary-pink;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    @include transition();

    &:hover {
      color: color.adjust($primary-pink, $lightness: -10%);
      text-decoration: none;
    }
  }
}

// Dark theme footer
body.dark-theme .notifications-footer {
  border-top-color: $dark-surface-light;

  .view-all-btn {
    color: $secondary-pink;

    &:hover {
      color: color.adjust($secondary-pink, $lightness: 10%);
    }
  }
}

// Mobile responsive
@include media-breakpoint-down(md) {
  .notifications-dropdown {
    width: 320px;
    right: -1rem;
  }

  .notification-item {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .notification-icon {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }

  .notification-title {
    font-size: 0.8125rem;
  }

  .notification-message {
    font-size: 0.6875rem;
  }
}
