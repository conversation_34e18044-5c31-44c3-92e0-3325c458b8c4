// Button styles
@use 'sass:color';
@use '../variables' as *;
@use '../mixins' as *;

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: $btn-padding-y $btn-padding-x;
  font-size: $btn-font-size;
  font-weight: $font-weight-medium;
  line-height: $btn-line-height;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: $btn-border-radius;
  @include transition();

  // Ensure text is always visible
  &:not(.btn-link) {
    color: inherit;
  }

  &:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba($primary-pink, 0.25);
  }

  &:disabled,
  &.disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }

  &:not(:disabled):not(.disabled) {
    cursor: pointer;
  }

  // Remove default button styles
  &:not(.btn-link) {
    background-image: none;
  }
}

// Button sizes
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: $font-size-sm;
  border-radius: $border-radius-sm;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: $font-size-lg;
  border-radius: $border-radius-lg;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: $font-size-xl;
  border-radius: $border-radius-xl;
}

// Button variants
.btn-primary {
  @include button-variant($primary-pink, $primary-pink, white);
}

.btn-secondary {
  @include button-variant($primary-blue, $primary-blue, white);
}

.btn-success {
  @include button-variant($success, $success, white);
}

.btn-warning {
  @include button-variant($warning, $warning, $dark-bg);
}

.btn-danger {
  @include button-variant($danger, $danger, white);
}

.btn-info {
  @include button-variant($info, $info, white);
}

.btn-light {
  @include button-variant($light-surface, $border-color, $light-text);
}

.btn-dark {
  @include button-variant($dark-surface, $dark-surface, white);
}

.btn-admin {
  background: linear-gradient(135deg, #6f42c1, #e83e8c);
  border-color: #6f42c1;
  color: white;
  font-weight: $font-weight-semibold;

  &:hover {
    background: linear-gradient(135deg, #5a2d91, #d91a72);
    border-color: #5a2d91;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.5);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled,
  &.disabled {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    border-color: #6f42c1;
    opacity: 0.65;
  }
}

// Outline buttons
.btn-outline {
  color: $primary-pink;
  border-color: $primary-pink;
  background-color: transparent;

  &:hover {
    color: white;
    background-color: $primary-pink;
    border-color: $primary-pink;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($primary-pink, 0.5);
  }

  &:disabled,
  &.disabled {
    color: $primary-pink;
    background-color: transparent;
  }
}

.btn-outline-secondary {
  color: $primary-blue;
  border-color: $primary-blue;
  background-color: transparent;

  &:hover {
    color: white;
    background-color: $primary-blue;
    border-color: $primary-blue;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($primary-blue, 0.5);
  }
}

.btn-outline-success {
  color: $success;
  border-color: $success;
  background-color: transparent;

  &:hover {
    color: white;
    background-color: $success;
    border-color: $success;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($success, 0.5);
  }
}

.btn-outline-warning {
  color: $warning;
  border-color: $warning;
  background-color: transparent;

  &:hover {
    color: $dark-bg;
    background-color: $warning;
    border-color: $warning;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($warning, 0.5);
  }
}

.btn-outline-danger {
  color: $danger;
  border-color: $danger;
  background-color: transparent;

  &:hover {
    color: white;
    background-color: $danger;
    border-color: $danger;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($danger, 0.5);
  }
}

.btn-outline-info {
  color: $info;
  border-color: $info;
  background-color: transparent;

  &:hover {
    color: white;
    background-color: $info;
    border-color: $info;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($info, 0.5);
  }
}

.btn-outline-light {
  color: $light-text-muted;
  border-color: $border-color;
  background-color: transparent;

  &:hover {
    color: $light-text;
    background-color: $light-surface;
    border-color: $border-color;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($border-color, 0.5);
  }
}

.btn-outline-dark {
  color: $dark-surface;
  border-color: $dark-surface;
  background-color: transparent;

  &:hover {
    color: white;
    background-color: $dark-surface;
    border-color: $dark-surface;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($dark-surface, 0.5);
  }
}

// Dark theme button adjustments
body.dark-theme {
  .btn-light {
    @include button-variant($dark-surface-light, $dark-surface-light, $dark-text);
  }

  .btn-outline-light {
    color: $dark-text-muted;
    border-color: $dark-surface-light;

    &:hover {
      color: $dark-text;
      background-color: $dark-surface-light;
      border-color: $dark-surface-light;
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba($dark-surface-light, 0.5);
    }
  }
}

// Link buttons
.btn-link {
  font-weight: $font-weight-normal;
  color: $primary-pink;
  text-decoration: none;
  background-color: transparent;
  border: 0;

  &:hover {
    color: color.scale($primary-pink, $lightness: -15%);
    text-decoration: underline;
  }

  &:focus {
    text-decoration: underline;
    box-shadow: none;
  }

  &:disabled,
  &.disabled {
    color: $light-text-muted;
    pointer-events: none;
  }
}

// Dark theme link buttons
body.dark-theme .btn-link {
  color: $secondary-pink;

  &:hover {
    color: color.scale($secondary-pink, $lightness: 15%);
  }

  &:disabled,
  &.disabled {
    color: $dark-text-muted;
  }
}

// Button groups
.btn-group {
  position: relative;
  display: inline-flex;
  vertical-align: middle;

  > .btn {
    position: relative;
    flex: 1 1 auto;

    &:hover {
      z-index: 1;
    }

    &:focus,
    &:active,
    &.active {
      z-index: 1;
    }
  }

  .btn + .btn,
  .btn + .btn-group,
  .btn-group + .btn,
  .btn-group + .btn-group {
    margin-left: -1px;
  }

  > .btn:not(:last-child):not(.dropdown-toggle),
  > .btn-group:not(:last-child) > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  > .btn:not(:first-child),
  > .btn-group:not(:first-child) > .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

// Block buttons
.btn-block {
  display: block;
  width: 100%;

  + .btn-block {
    margin-top: 0.5rem;
  }
}

// Floating action button
.btn-fab {
  @include square(56px);
  border-radius: 50%;
  box-shadow: $box-shadow;
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: $z-index-fixed;
  @include transition(all, 0.3s);

  &:hover {
    transform: scale(1.1);
    box-shadow: $box-shadow-lg;
  }

  i {
    font-size: 1.5rem;
  }
}

// Loading state
.btn-loading {
  position: relative;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .btn-text {
    opacity: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
