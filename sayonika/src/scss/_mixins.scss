// Sayonika SCSS Mixins
@use 'sass:map';
@use 'sass:color';
@use 'variables' as *;

// Media query mixin
@mixin media-breakpoint-up($name) {
  $min: map.get($breakpoints, $name);
  @if $min != 0 {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-down($name) {
  $max: map.get($breakpoints, $name) - 0.02;
  @media (max-width: $max) {
    @content;
  }
}

@mixin media-breakpoint-between($lower, $upper) {
  $min: map.get($breakpoints, $lower);
  $max: map.get($breakpoints, $upper) - 0.02;

  @if $min != 0 and $max != 0 {
    @media (min-width: $min) and (max-width: $max) {
      @content;
    }
  } @else if $max == 0 {
    @include media-breakpoint-up($lower) {
      @content;
    }
  } @else if $min == 0 {
    @include media-breakpoint-down($upper) {
      @content;
    }
  }
}

// Button mixin
@mixin button-variant($background, $border, $color: #fff) {
  color: $color;
  background-color: $background;
  border-color: $border;

  &:hover {
    color: $color;
    background-color: color.scale($background, $lightness: -7.5%);
    border-color: color.scale($border, $lightness: -10%);
  }

  &:focus,
  &.focus {
    color: $color;
    background-color: color.scale($background, $lightness: -7.5%);
    border-color: color.scale($border, $lightness: -10%);
    box-shadow: 0 0 0 0.2rem rgba($border, 0.5);
  }

  &:disabled,
  &.disabled {
    color: $color;
    background-color: $background;
    border-color: $border;
    opacity: 0.65;
  }

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active {
    color: $color;
    background-color: color.scale($background, $lightness: -10%);
    border-color: color.scale($border, $lightness: -12.5%);

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba($border, 0.5);
    }
  }
}

// Card mixin
@mixin card-variant($background, $border: $background) {
  background-color: $background;
  border-color: $border;
}

// Gradient mixin
@mixin gradient-x($start-color: #555, $end-color: #333, $start-percent: 0%, $end-percent: 100%) {
  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);
  background-repeat: repeat-x;
}

@mixin gradient-y($start-color: #555, $end-color: #333, $start-percent: 0%, $end-percent: 100%) {
  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);
  background-repeat: repeat-x;
}

@mixin gradient-directional($start-color: #555, $end-color: #333, $deg: 45deg) {
  background-image: linear-gradient($deg, $start-color, $end-color);
  background-repeat: repeat-x;
}

// Text truncation
@mixin text-truncate() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Clearfix
@mixin clearfix() {
  &::after {
    display: block;
    clear: both;
    content: "";
  }
}

// Center block
@mixin center-block() {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

// Flexbox utilities
@mixin flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between() {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Animation mixins
@mixin transition($property: all, $duration: 0.2s, $timing: ease-in-out, $delay: 0s) {
  transition: $property $duration $timing $delay;
}

@mixin animation($name, $duration: 1s, $timing: ease, $delay: 0s, $iteration: 1, $direction: normal, $fill: both) {
  animation: $name $duration $timing $delay $iteration $direction $fill;
}

// Box shadow mixins
@mixin box-shadow($shadow...) {
  box-shadow: $shadow;
}

@mixin hover-lift() {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

// Border radius mixins
@mixin border-radius($radius: $border-radius) {
  border-radius: $radius;
}

@mixin border-top-radius($radius: $border-radius) {
  border-top-left-radius: $radius;
  border-top-right-radius: $radius;
}

@mixin border-bottom-radius($radius: $border-radius) {
  border-bottom-left-radius: $radius;
  border-bottom-right-radius: $radius;
}

@mixin border-left-radius($radius: $border-radius) {
  border-top-left-radius: $radius;
  border-bottom-left-radius: $radius;
}

@mixin border-right-radius($radius: $border-radius) {
  border-top-right-radius: $radius;
  border-bottom-right-radius: $radius;
}

// Positioning mixins
@mixin position($position, $top: null, $right: null, $bottom: null, $left: null) {
  position: $position;
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
}

@mixin absolute($top: null, $right: null, $bottom: null, $left: null) {
  @include position(absolute, $top, $right, $bottom, $left);
}

@mixin relative($top: null, $right: null, $bottom: null, $left: null) {
  @include position(relative, $top, $right, $bottom, $left);
}

@mixin fixed($top: null, $right: null, $bottom: null, $left: null) {
  @include position(fixed, $top, $right, $bottom, $left);
}

// Size mixins
@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

@mixin square($size) {
  @include size($size, $size);
}
