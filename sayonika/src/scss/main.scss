// Sayonika Main Stylesheet

// Import Sass modules
@use 'sass:color';
@use 'sass:map';
@use 'sass:math';

// Import variables and mixins
@use 'variables' as *;
@use 'mixins' as *;

// Import base styles
@use 'base';

// Import component styles
@use 'components/header';
@use 'components/footer';
@use 'components/buttons';
@use 'components/cards';
@use 'components/forms';
@use 'components/modals';
@use 'components/mod-management';
@use 'components/notifications';

// Import page-specific styles
@use 'pages/home';
@use 'pages/browse';
@use 'pages/mod-detail';
@use 'pages/auth';
@use 'pages/profile';
@use 'pages/admin';
@use 'pages/legal';
@use 'pages/settings';
@use 'pages/merge-accounts';
@use 'pages/docs';
@use 'pages/help';
@use 'pages/contact';
@use 'pages/upload';

// Import utility classes
@use 'utilities';
