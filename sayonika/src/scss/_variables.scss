// Sayonika SCSS Variables

// Colors - DDLC inspired palette
$primary-pink: #ff6b9d;
$primary-blue: #4ecdc4;
$primary-purple: #a8e6cf;
$primary-yellow: #ffd93d;

$secondary-pink: #ff8fab;
$secondary-blue: #6dd5db;
$secondary-purple: #b8f0d6;
$secondary-yellow: #ffe066;

// Dark theme colors
$dark-bg: #1a1a1a;
$dark-surface: #2d2d2d;
$dark-surface-light: #3d3d3d;
$dark-text: #ffffff;
$dark-text-muted: #cccccc;

// Light theme colors
$light-bg: #ffffff;
$light-surface: #f8f9fa;
$light-surface-dark: #e9ecef;
$light-text: #333333;
$light-text-muted: #666666;

// Status colors
$success: #28a745;
$warning: #ffc107;
$danger: #dc3545;
$info: #17a2b8;

// Typography
$font-family-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
$font-family-heading: 'Arial', sans-serif;
$font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;

$font-size-base: 16px;
$font-size-sm: 14px;
$font-size-lg: 18px;
$font-size-xl: 20px;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$line-height-base: 1.5;
$line-height-sm: 1.25;
$line-height-lg: 1.75;

// Spacing
$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3
);

// Borders
$border-width: 1px;
$border-color: #dee2e6;
$border-radius: 0.375rem;
$border-radius-sm: 0.25rem;
$border-radius-lg: 0.5rem;
$border-radius-xl: 1rem;

// Shadows
$box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
$box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

// Breakpoints
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// Container max widths
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);

// Grid
$grid-columns: 12;
$grid-gutter-width: 1.5rem;

// Z-index layers
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// Transitions
$transition-base: all 0.2s ease-in-out;
$transition-fade: opacity 0.15s linear;
$transition-collapse: height 0.35s ease;

// Component specific variables
$navbar-height: 60px;
$footer-height: 80px;
$sidebar-width: 250px;

// Card variables
$card-spacer-y: 1rem;
$card-spacer-x: 1rem;
$card-border-width: $border-width;
$card-border-radius: $border-radius;
$card-border-color: $border-color;

// Button variables
$btn-padding-y: 0.5rem;
$btn-padding-x: 1rem;
$btn-font-size: $font-size-base;
$btn-line-height: $line-height-base;
$btn-border-radius: $border-radius;

// Form variables
$input-padding-y: 0.5rem;
$input-padding-x: 0.75rem;
$input-font-size: $font-size-base;
$input-line-height: $line-height-base;
$input-border-radius: $border-radius;
$input-border-color: $border-color;
