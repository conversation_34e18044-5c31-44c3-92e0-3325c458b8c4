// Base styles for Sayonika
@use 'sass:color';
@use 'sass:math';
@use 'sass:map';
@use 'variables' as *;
@use 'mixins' as *;

// Reset and normalize
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  margin: 0;
  font-family: $font-family-base;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-base;
  color: $light-text;
  background-color: $light-bg;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

// Dark theme
body.dark-theme {
  color: $dark-text;
  background-color: $dark-bg;
}

// Typography
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-family: $font-family-heading;
  font-weight: $font-weight-medium;
  line-height: 1.2;
  color: inherit;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

a {
  color: $primary-pink;
  text-decoration: none;
  @include transition(color);

  &:hover {
    color: color.scale($primary-pink, $lightness: -15%);
    text-decoration: underline;
  }

  &:focus {
    outline: thin dotted;
    outline-offset: 2px;
  }
}

// Dark theme links
body.dark-theme a {
  color: $secondary-pink;

  &:hover {
    color: color.scale($secondary-pink, $lightness: 15%);
  }
}

// Lists
ul, ol {
  margin-top: 0;
  margin-bottom: 1rem;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.25rem;
}

// Images
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border-style: none;
}

// Tables
table {
  border-collapse: collapse;
  width: 100%;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid $border-color;
}

th {
  font-weight: $font-weight-medium;
  background-color: $light-surface;
}

// Dark theme tables
body.dark-theme {
  th, td {
    border-bottom-color: $dark-surface-light;
  }

  th {
    background-color: $dark-surface;
  }
}

// Code
code, pre {
  font-family: $font-family-mono;
  font-size: 0.875em;
}

code {
  padding: 0.2rem 0.4rem;
  background-color: $light-surface;
  border-radius: $border-radius-sm;
  color: $danger;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: $light-surface;
  border-radius: $border-radius;
  overflow-x: auto;

  code {
    padding: 0;
    background-color: transparent;
    color: inherit;
  }
}

// Dark theme code
body.dark-theme {
  code {
    background-color: $dark-surface;
    color: $primary-yellow;
  }

  pre {
    background-color: $dark-surface;
  }
}

// Layout
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;

  @each $breakpoint, $max-width in $container-max-widths {
    @include media-breakpoint-up($breakpoint) {
      max-width: $max-width;
    }
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

// Grid system
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

// Generate column classes
@for $i from 1 through $grid-columns {
  .col-#{$i} {
    flex: 0 0 math.percentage(math.div($i, $grid-columns));
    max-width: math.percentage(math.div($i, $grid-columns));
    padding-right: 15px;
    padding-left: 15px;
  }
}

// Responsive columns
@each $breakpoint in map.keys($breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    @for $i from 1 through $grid-columns {
      .col-#{$breakpoint}-#{$i} {
        flex: 0 0 math.percentage(math.div($i, $grid-columns));
        max-width: math.percentage(math.div($i, $grid-columns));
        padding-right: 15px;
        padding-left: 15px;
      }
    }
  }
}

// Utility classes
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-muted { color: $light-text-muted; }
.text-primary { color: $primary-pink; }
.text-success { color: $success; }
.text-warning { color: $warning; }
.text-danger { color: $danger; }
.text-info { color: $info; }

// Dark theme text utilities
body.dark-theme {
  .text-muted { color: $dark-text-muted; }
}

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }

// Spacing utilities
@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in $spacers {
    .#{$abbrev}-#{$size} { #{$prop}: $length; }
    .#{$abbrev}t-#{$size} { #{$prop}-top: $length; }
    .#{$abbrev}r-#{$size} { #{$prop}-right: $length; }
    .#{$abbrev}b-#{$size} { #{$prop}-bottom: $length; }
    .#{$abbrev}l-#{$size} { #{$prop}-left: $length; }
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length;
      #{$prop}-right: $length;
    }
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length;
      #{$prop}-bottom: $length;
    }
  }
}
