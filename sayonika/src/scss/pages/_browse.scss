// Browse page styles
@use '../variables' as *;
@use '../mixins' as *;

.browse-page {
  padding: 2rem 0;
}

.browse-header {
  margin-bottom: 2rem;
  text-align: center;

  h1 {
    color: $light-text;
    margin-bottom: 1rem;
  }

  p {
    color: $light-text-muted;
    font-size: 1.125rem;
  }
}

// Dark theme browse header
body.dark-theme .browse-header {
  h1 {
    color: $dark-text;
  }

  p {
    color: $dark-text-muted;
  }
}

.browse-filters {
  background-color: $light-surface;
  border-radius: $border-radius-lg;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: $box-shadow-sm;
}

// Dark theme browse filters
body.dark-theme .browse-filters {
  background-color: $dark-surface;
}

.filters-row {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 1rem;
  align-items: end;

  @include media-breakpoint-down(md) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.search-group {
  position: relative;

  .search-input {
    padding-right: 3rem;
  }

  .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: $light-text-muted;
    pointer-events: none;
  }
}

// Dark theme search icon
body.dark-theme .search-group .search-icon {
  color: $dark-text-muted;
}

.filter-group {
  display: flex;
  gap: 1rem;
  align-items: end;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}

.sort-select {
  min-width: 150px;
}

.view-toggle {
  display: flex;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  overflow: hidden;

  .view-btn {
    background: none;
    border: none;
    padding: 0.5rem 0.75rem;
    color: $light-text-muted;
    cursor: pointer;
    @include transition();

    &:hover {
      background-color: rgba($primary-pink, 0.1);
      color: $primary-pink;
    }

    &.active {
      background-color: $primary-pink;
      color: white;
    }

    i {
      font-size: 1rem;
    }
  }
}

// Dark theme view toggle
body.dark-theme .view-toggle {
  border-color: $dark-surface-light;

  .view-btn {
    color: $dark-text-muted;

    &:hover {
      background-color: rgba($secondary-pink, 0.1);
      color: $secondary-pink;
    }

    &.active {
      background-color: $secondary-pink;
      color: $dark-bg;
    }
  }
}

.browse-results {
  margin-bottom: 2rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid $border-color;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

// Dark theme results header
body.dark-theme .results-header {
  border-bottom-color: $dark-surface-light;
}

.results-count {
  color: $light-text;
  font-weight: $font-weight-medium;

  .count {
    color: $primary-pink;
  }
}

// Dark theme results count
body.dark-theme .results-count {
  color: $dark-text;

  .count {
    color: $secondary-pink;
  }
}

.active-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tag {
  background-color: rgba($primary-pink, 0.1);
  color: $primary-pink;
  padding: 0.25rem 0.75rem;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .remove-filter {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    font-size: 0.875rem;

    &:hover {
      opacity: 0.7;
    }
  }
}

// Dark theme filter tag
body.dark-theme .filter-tag {
  background-color: rgba($secondary-pink, 0.1);
  color: $secondary-pink;
}

// Grid view
.mods-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// List view
.mods-list-view {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .mod-card {
    display: flex;
    flex-direction: row;
    height: auto;

    .mod-card-image {
      width: 200px;
      height: 150px;
      flex-shrink: 0;

      @include media-breakpoint-down(sm) {
        width: 120px;
        height: 90px;
      }
    }

    .mod-card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .mod-card-description {
      -webkit-line-clamp: 2;
    }
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 3rem;
}

.pagination-btn {
  background: none;
  border: 1px solid $border-color;
  color: $light-text;
  padding: 0.5rem 0.75rem;
  border-radius: $border-radius;
  cursor: pointer;
  @include transition();

  &:hover:not(:disabled) {
    background-color: $primary-pink;
    border-color: $primary-pink;
    color: white;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.active {
    background-color: $primary-pink;
    border-color: $primary-pink;
    color: white;
  }
}

// Dark theme pagination
body.dark-theme .pagination-btn {
  border-color: $dark-surface-light;
  color: $dark-text;

  &:hover:not(:disabled) {
    background-color: $secondary-pink;
    border-color: $secondary-pink;
    color: $dark-bg;
  }

  &.active {
    background-color: $secondary-pink;
    border-color: $secondary-pink;
    color: $dark-bg;
  }
}

.pagination-info {
  color: $light-text-muted;
  font-size: $font-size-sm;
  margin: 0 1rem;
}

// Dark theme pagination info
body.dark-theme .pagination-info {
  color: $dark-text-muted;
}

// Empty state
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: $light-text-muted;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    color: $light-text;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
  }
}

// Dark theme empty state
body.dark-theme .empty-state {
  color: $dark-text-muted;

  h3 {
    color: $dark-text;
  }
}

// Loading state
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem 2rem;
  color: $light-text-muted;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba($primary-pink, 0.2);
    border-top-color: $primary-pink;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 1rem;
  }
}

// Dark theme loading state
body.dark-theme .loading-state {
  color: $dark-text-muted;

  .loading-spinner {
    border-color: rgba($secondary-pink, 0.2);
    border-top-color: $secondary-pink;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
