# Sayonika Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
# Base URL for the website (used for links, OAuth callbacks, etc.)
BASE_URL=https://your-domain.com
# HTTPS is always enabled (requires SSL certificates)
ENABLE_HTTPS=true
# SSL Certificate paths
SSL_KEY_PATH=./csr/cert.key
SSL_CERT_PATH=./csr/cert.crt

# Database Configuration
DATABASE_PATH=./database/sayonika.db

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# CORS Configuration (automatically includes HTTP/HTTPS variants)
ALLOWED_ORIGINS=${BASE_URL}

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_PATH=./uploads

# Email Configuration (for notifications and contact forms)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# OAuth Configuration
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_CALLBACK_URL=https://your-domain.com/auth/github/callback

DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret
DISCORD_CALLBACK_URL=https://your-domain.com/auth/discord/callback

# External Services
DISCORD_WEBHOOK_URL=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# Proxy Configuration (for Cloudflare and reverse proxies)
TRUST_PROXY_HOPS=0

# Development and Debug
DEBUG=false
SESSIONS_DATABASE_PATH=./database/sessions.db