# Quick fix for nginx upload issues with Sayonika external URLs
# Add these directives to your existing nginx server block

# Global settings (add to your server block)
client_max_body_size 2G;  # Allow large requests
client_body_timeout 300s;  # 5 minutes timeout
client_header_timeout 60s;
send_timeout 300s;

# Special handling for upload endpoints (add this location block)
location /api/mods {
    # Even larger limits for mod uploads
    client_max_body_size 2G;
    client_body_timeout 600s;  # 10 minutes for very large uploads
    
    # Disable request buffering for better handling of large requests
    proxy_request_buffering off;
    proxy_buffering off;
    
    # Extended timeouts
    proxy_connect_timeout 60s;
    proxy_send_timeout 600s;
    proxy_read_timeout 600s;
    
    # Standard proxy settings (adjust localhost:3000 to your app's address)
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}

# If you're also having issues with other API endpoints, add this too:
location /api/ {
    client_max_body_size 100M;  # Smaller but still generous limit for other API calls
    client_body_timeout 120s;
    
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    
    proxy_connect_timeout 60s;
    proxy_send_timeout 120s;
    proxy_read_timeout 120s;
}
